[project]
name = "aigc-rag-service"
version = "0.1.0"
description = "爱用aigc rag服务"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "dify-plugin>=0.2.1",
    "fastapi>=0.115.12",
    "httpx>=0.27.0",
    "lark-oapi>=1.4.14",
    "oss2>=2.19.1",
    "pycryptodome>=3.22.0",
    "pydantic-settings>=2.9.1",
    "pymysql>=1.1.1",
    "pytest-playwright>=0.7.0",
    "python-ghost-cursor>=0.1.1",
    "python-json-logger>=3.3.0",
    "pyyaml>=6.0.2",
    "redis>=5.2.1",
    "requests>=2.32.3",
    "sqlalchemy>=2.0.40",
    "uvicorn>=0.34.2",
]

[project.scripts]
aigc-rag-service = "app:main"
#
#[tool.poetry.dependencies]
#python = "^3.12"
#fastapi = "^0.112.2"
#sqlalchemy = "^2.0.32"
#uvicorn = {extras = ["standard"], version = "^0.30.6"}
#pydantic-settings = "^2.4.0"
#redis = "^5.0.8"
#pymysql = "^1.1.1"
#requests = "^2.32.3"
#
#
#[tool.poetry.group.dev.dependencies]
#sqlacodegen = "3.0.0rc5"
#
#[build-system]
#requires = ["poetry-config"]
#build-backend = "poetry.config.masonry.api"
#
#[plugins]
#[plugins.pypi_mirror]
#url = "https://pypi.tuna.tsinghua.edu.cn/simple"
