FROM python:3.12-slim-bookworm

ARG PLUGIN_VERSION=0.0.14

WORKDIR /srv/

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple 

# set timezone
ENV TZ=Asia/Shanghai
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

ENV TIKTOKEN_CACHE_DIR=/app/.tiktoken
RUN pip install --no-cache-dir tiktoken
RUN python3 -c "import tiktoken; encodings = ['o200k_base', 'cl100k_base', 'p50k_base', 'r50k_base', 'p50k_edit', 'gpt2']; [tiktoken.get_encoding(encoding).special_tokens_set for encoding in encodings]"


COPY langgenius-azure_openai_${PLUGIN_VERSION}.difypkg ./
RUN unzip langgenius-azure_openai_${PLUGIN_VERSION}.difypkg && rm -f langgenius-azure_openai_${PLUGIN_VERSION}.difypkg
RUN pip install --no-cache-dir -r /srv/requirements.txt 
RUN pip install --no-cache-dir -U dify_plugin

ENV INSTALL_METHOD=serverless SERVERLESS_PORT=8080  SERVERLESS_WORKER_CONNECTIONS=1000 SERVERLESS_WORKERS=5 SERVERLESS_THREADS=5

EXPOSE 8080

ENTRYPOINT ["python", "-m", "main"]