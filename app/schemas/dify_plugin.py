from typing import Generic, Optional, TypeVar
from dify_plugin.core.entities.plugin.request import ModelInvokeLLMRequest
from pydantic import BaseModel


T = TypeVar("T", bound=(BaseModel | dict | list | bool | str))


class PluginServerlessRequest(BaseModel):
    event: str
    session_id: str
    data: dict

class PluginServerlessResponse(BaseModel):
    event: str
    session_id: str
    data: Optional[dict] = None


class PluginDaemonBasicRequest(BaseModel):
    user_id: str
    data: dict

class PluginDaemonBasicResponse(BaseModel, Generic[T]):
    """
    Basic response from plugin daemon.
    """

    code: int
    message: str
    data: Optional[T | dict] = None
