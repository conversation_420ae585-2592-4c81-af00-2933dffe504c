from typing import List, Optional

from pydantic import BaseModel, ConfigDict, Field

class LogGetByIdRequest(BaseModel):
    """日志查询请求参数"""
    index: str
    doc_id: str

class LogQueryRequest(BaseModel):
    """日志查询请求参数"""
    index: str = Field(description="ES索引名称")
    query_string: str = Field(description="查询字符串，支持ES的query_string语法")
    timestamp: str = Field(description="时间戳，格式为ISO 8601，例如：2024-03-20T10:00:00Z")

class Tag(BaseModel):
    """标签模型"""

    metric: Optional[str] = None  # 标签名称
    value: Optional[str | int | float] = None  # 标签内容


class GrafanaRequest(BaseModel):
    """Grafana请求模型"""

    message: Optional[str] = None  # 告警消息内容
    title: Optional[str] = None  # 告警标题
    rule_url: Optional[str] = Field(default=None, alias="ruleUrl")  # 告警返回链接网址
    eval_matches: Optional[List[Tag]] = Field(
        default=None, alias="evalMatches"
    )  # 匹配标签体


class GitHubSearchRequest(BaseModel):
    """GitHub源码搜索请求参数"""

    model_config = ConfigDict(from_attributes=True)

    is_dev_branch: bool = Field(description="是否是dev分支")
    class_name: str = Field(description="类名")
    method_name: Optional[str] = Field(default=None, description="方法名")
    line_number: Optional[int] = Field(default=None, description="行号")
    fetch_source_content: bool = Field(default=False, description="是否获取源码具体内容")
    
class GitHubSourceInfo(BaseModel):
    """GitHub源码信息"""

    model_config = ConfigDict(from_attributes=True)

    file_name: Optional[str] = None
    file_path: Optional[str] = None
    repository: Optional[str] = None
    html_url: Optional[str] = None
    file_content: Optional[str] = None


class ExceptionDetail(BaseModel):
    """异常详情"""

    model_config = ConfigDict(from_attributes=True)

    class_name: Optional[str] = None
    method_name: Optional[str] = None
    line_number: Optional[int] = None
    exception_class: Optional[str] = None
    github_source: Optional[GitHubSourceInfo] = None


class ErrorInfo(BaseModel):
    """错误信息"""

    model_config = ConfigDict(from_attributes=True)

    error: Optional[str] = None
    count: Optional[int] = None
    index: Optional[str] = None
    timestamp: Optional[str] = None
    id: Optional[str] = None
    exception_details: Optional[ExceptionDetail] = None

class AnalysisResult(BaseModel):
    """分析结果"""

    model_config = ConfigDict(from_attributes=True)

    errors: Optional[List[ErrorInfo]] = None


class GrafanaAlertAnalysisResponse(BaseModel):
    """Grafana告警分析响应"""

    model_config = ConfigDict(from_attributes=True)

    # 成功情况的字段
    grafana_alert_link: Optional[str] = None
    kibana_log_link: Optional[str] = None
    analysis_result: Optional[AnalysisResult] = None

    # 错误或消息情况的字段
    error: Optional[str] = None
    message: Optional[str] = None
