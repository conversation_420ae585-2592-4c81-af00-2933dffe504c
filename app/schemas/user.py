from typing import Optional

from pydantic import BaseModel, ConfigDict
from datetime import datetime

class UserTradeExt(BaseModel):

    model_config = ConfigDict(from_attributes=True)
    seller_id: str
    seller_nick: Optional[str] = None
    store_id: Optional[str] = None
    corp_id: Optional[str] = None
    db_status: Optional[int] = None
    pull_status: Optional[int] = None
    opened_app_names: Optional[str] = None
    session_status: Optional[int] = None
    api_status: Optional[int] = None
    top_status: Optional[str] = None
    db2search_status: Optional[int] = None
    top_trade_count: Optional[int] = None
    downgrade_tag: Optional[str] = None
    db_id: Optional[int] = None
    searchdb_id: Optional[int] = None
    list_id: Optional[int] = None
    gray_level: Optional[int] = None
    pull_start_datetime: Optional[datetime] = None
    pull_end_datetime: Optional[datetime] = None
    pull_end_point: Optional[int] = None
    gmt_modified: Optional[datetime] = None
    gmt_create: Optional[datetime] = None
    member_id: Optional[str] = None
