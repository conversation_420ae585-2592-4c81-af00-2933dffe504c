import logging

from fastapi import APIRouter

from app.schemas.base import CommonResponse
from app.schemas.analyzer import GrafanaRequest
from app.services.analyzer.analyzer_service import analyzer_service

# 设置日志记录器
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/rag/grafana", include_in_schema=False)


@router.post("/logAlarm/{hookId}", response_model=CommonResponse)
async def log_alarm(hookId: str, request: GrafanaRequest):
    """
    处理Grafana日志告警
    """
    # 记录告警请求体
    logger.info(f"log告警请求体: {request.model_dump_json()}")

    await analyzer_service.handle_grafana_callback(hookId, request)

    # 这里可以添加具体的告警处理逻辑
    # 例如：发送邮件、短信、钉钉通知等

    return CommonResponse(body=None)
