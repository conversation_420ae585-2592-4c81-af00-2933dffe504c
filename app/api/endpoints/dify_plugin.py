import json
import time
import traceback
import uuid
import logging
from typing import Any

import httpx
from fastapi import APIRouter, Header, Request, Response
from fastapi.responses import StreamingResponse

from app.schemas.dify_plugin import PluginDaemonBasicRequest, PluginServerlessResponse
from app.core.config import settings

X_PLUGIN_ID = "X-Plugin-ID"
X_API_KEY = "X-Api-Key"

logging = logging.getLogger(__name__)

router = APIRouter(prefix="/plugin/{tenant_id}/dispatch", include_in_schema=False)

_client = httpx.AsyncClient(timeout=240)


@router.post("/llm/invoke")
async def invoke_llm(
    http_request: Request,
    requset: PluginDaemonBasicRequest,
    tenant_id: str,
    plugin_id: str = Header(default="", alias=X_PLUGIN_ID),
    plugin_api_key: str = Header(default="", alias=X_API_KEY),
) -> StreamingResponse:
    """
    LLM invoke
    """
    return await __forward_request(
        "invoke_llm", plugin_id, plugin_api_key, requset, http_request
    )


@router.post("/llm/num_tokens")
async def num_tokens(
    http_request: Request,
    requset: PluginDaemonBasicRequest,
    tenant_id: str,
    plugin_id: str = Header(default="", alias=X_PLUGIN_ID),
    plugin_api_key: str = Header(default="", alias=X_API_KEY),
) -> StreamingResponse:
    """
    num tokens
    """
    return await __forward_request(
        "get_llm_num_tokens", plugin_id, plugin_api_key, requset, http_request
    )


@router.post("/rerank/invoke")
async def invoke_rerank(
    http_request: Request,
    requset: PluginDaemonBasicRequest,
    tenant_id: str,
    plugin_id: str = Header(default="", alias=X_PLUGIN_ID),
    plugin_api_key: str = Header(default="", alias=X_API_KEY),
) -> StreamingResponse:
    """
    invoke rerank
    """
    return await __forward_request(
        "invoke_rerank", plugin_id, plugin_api_key, requset, http_request
    )

@router.post("/model/schema")
async def invoke_rerank(
    http_request: Request,
    requset: PluginDaemonBasicRequest,
    tenant_id: str,
    plugin_id: str = Header(default="", alias=X_PLUGIN_ID),
    plugin_api_key: str = Header(default="", alias=X_API_KEY),
) -> StreamingResponse:
    """
    invoke rerank
    """
    return await __forward_request(
        "get_ai_model_schemas", plugin_id, plugin_api_key, requset, http_request
    )


@router.post("/text_embedding/num_tokens")
async def invoke_rerank(
    http_request: Request,
    requset: PluginDaemonBasicRequest,
    tenant_id: str,
    plugin_id: str = Header(default="", alias=X_PLUGIN_ID),
    plugin_api_key: str = Header(default="", alias=X_API_KEY),
) -> StreamingResponse:
    """
    invoke rerank
    """
    return await __forward_request(
        "get_text_embedding_num_tokens", plugin_id, plugin_api_key, requset, http_request
    )


@router.post("/text_embedding/invoke")
async def invoke_rerank(
    http_request: Request,
    requset: PluginDaemonBasicRequest,
    tenant_id: str,
    plugin_id: str = Header(default="", alias=X_PLUGIN_ID),
    plugin_api_key: str = Header(default="", alias=X_API_KEY),
) -> StreamingResponse:
    """
    invoke rerank
    """
    return await __forward_request(
        "invoke_text_embedding", plugin_id, plugin_api_key, requset, http_request
    )

async def __forward_request(
    action: str,
    plugin_id,
    plugin_api_key,
    requset: PluginDaemonBasicRequest,
    http_request: Request,
) -> StreamingResponse:
    """
    代理请求
    """
    # print(f"request >>>>>> {requset}")
    if plugin_id in settings().DIFY_PLUGIN_SERVERLESS_URLS:
        return await __forward_to_plugin(action, plugin_id, requset)
    else:
        return await __forward_to_plugin_daemon(action, plugin_id, http_request)


async def __forward_to_plugin_daemon(
    action: str,
    plugin_id,
    http_request: Request,
) -> StreamingResponse:
    """
    代理请求到插件守护进程
    """
    server_url = f"{settings().DIFY_PLUGIN_DAEMON_URL}{http_request.url.path}"
    start_time = time.time()

    async def stream_generator():
        try:
            # 发送代理请求
            async with _client.stream(
                http_request.method,
                server_url,
                headers=dict(http_request.headers),
                content=await http_request.body(),
            ) as resp:
                resp.raise_for_status()
                # 返回代理响应
                async for chunk in resp.aiter_bytes():
                    yield chunk
        finally:
            total_time = time.time() - start_time
            logging.info(
                f"send_to_daemon/{action} {plugin_id} overall time: {total_time:.2f} seconds"
            )

    return StreamingResponse(
        stream_generator(),
        media_type="text/event-stream",
    )


async def __forward_to_plugin(
    action: str, plugin_id: str, requset: PluginDaemonBasicRequest
):
    """
    代理请求到独立插件
    """
    server_url = settings().DIFY_PLUGIN_SERVERLESS_URLS[plugin_id]
    requset.data["type"] = "model"
    requset.data["action"] = action
    requset.data["user_id"] = requset.user_id
    plugin_request = {
        "event": "request",
        "session_id": uuid.uuid4().hex,
        "data": requset.data,
    }
    start_time = time.time()

    async def stream_generator():
        # print(f"plugin_request ===>>> {plugin_request}")
        async with _client.stream(
            "POST",
            server_url,
            json=plugin_request,
        ) as http_rsp:
            try:
                http_rsp.raise_for_status()
                async for line in http_rsp.aiter_lines():
                    if line:
                        # print(f"plugin_response <<<=== {line}")
                        plugin_rsp = json.loads(line)
                        rsp_data = plugin_rsp["data"]
                        type = rsp_data["type"]
                        data = rsp_data["data"]

                        stream_out = {
                            "code": 0,
                            "message": "success",
                            "data": None,
                        }
                        if type == "stream":
                            stream_out["data"] = data
                        elif type == "error":
                            stream_out["code"] = -500
                            stream_out["message"] = json.dumps(data)
                        elif type == "end":
                            continue
                        yield f"data: {json.dumps(stream_out)}\n\n"
            except Exception as e:
                logging.error(traceback.format_exc())
                stream_out = {
                    "code": -500,
                    "message": traceback.format_exc(),
                    "data": None,
                }
                yield f"data: {json.dumps(stream_out)}\n\n"
            finally:
                total_time = time.time() - start_time
                logging.info(
                    f"invoke/{action} {plugin_id} overall time: {total_time:.2f} seconds"
                )

    return StreamingResponse(
        stream_generator(),
        media_type="text/event-stream",
    )


async def plugin_invoke(action: str, plugin_id: str, requset: PluginDaemonBasicRequest):
    """
    plugin invoke
    """
    server_url = settings().DIFY_PLUGIN_SERVERLESS_URLS[plugin_id]
    requset.data["type"] = "model"
    requset.data["action"] = action
    requset.data["user_id"] = requset.user_id
    plugin_request = {
        "event": "request",
        "session_id": uuid.uuid4().hex,
        "data": requset.data,
    }
    start_time = time.time()

    # print(f"plugin_request ===>>> {plugin_request}")
    http_rsp = await _client.request(
        "POST",
        server_url,
        json=plugin_request,
    )
    try:
        http_rsp.raise_for_status()
        return http_rsp.text
    except Exception as e:
        logging.error(traceback.format_exc())
        stream_out = {
            "code": -500,
            "message": traceback.format_exc(),
            "data": None,
        }
        return stream_out
    finally:
        total_time = time.time() - start_time
        logging.info(f"llm_invoke {plugin_id} overall time: {total_time:.2f} seconds")

    return
