import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPException

from app.schemas.base import CommonResponse
from app.schemas.crawl import CrawlItemInfoGetRequest, CrawlItemInfoGetResponse
from app.services.crawl.item_crawl_service import item_crawl_service

router = APIRouter(prefix="/rag/crawl", include_in_schema=False)


@router.api_route("/item.info.get", methods=["POST", "GET"])
async def item_info_get(
    request: CrawlItemInfoGetRequest,
) -> CommonResponse[CrawlItemInfoGetResponse]:
    """
    获取商品信息
    :param request:
    :return:
    """
    shipping = await item_crawl_service.crawl_item_shipping(
        request.item_id, request.sku_id
    )
    resp = CrawlItemInfoGetResponse(shipping=shipping)
    return CommonResponse(body=resp)
