import logging
from typing import List

from fastapi import APIRouter

from app.schemas.analyzer import GitHubSearchRequest, GitHubSourceInfo, LogGetByIdRequest, LogQueryRequest
from app.schemas.base import CommonResponse
from app.services.analyzer.eslog_service import eslog_service
from app.services.analyzer.github_service import github_service

# 设置日志记录器
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/rag/tools", tags=["tools"])


@router.post("/eslog/get", response_model=CommonResponse[dict])
async def get_by_id(
    request: LogGetByIdRequest,
) -> CommonResponse[dict]:
    """
    查询指定ID的日志记录

    Args:
        request: 日志查询请求参数，包含索引和文档ID

    Returns:
        日志记录内容
    """
    logger.info(f"请求体: {request.model_dump_json()}")

    result = await eslog_service.get_by_id(request.index, request.doc_id)

    if result is None:
        return CommonResponse(code=404, message="未找到指定的日志记录")

    return CommonResponse(body=result)


@router.post("/eslog/query", response_model=CommonResponse[List[dict]])
async def query(
    request: LogQueryRequest,
) -> CommonResponse[List[dict]]:
    """
    根据时间戳和查询字符串查询日志记录

    Args:
        request: 日志查询请求参数，包含索引、查询字符串和时间戳

    Returns:
        日志记录列表
    """
    logger.info(f"请求体: {request.model_dump_json()}")

    results = await eslog_service.query(
        request.index,
        request.query_string,
        request.timestamp
    )

    return CommonResponse(body=results)


@router.post("/github/search/source", response_model=CommonResponse[GitHubSourceInfo])
async def search_source(
    request: GitHubSearchRequest,
) -> CommonResponse[GitHubSourceInfo]:
    """
    通过GitHub API搜索源码

    Args:
        request: GitHub搜索请求参数

    Returns:
        GitHub源码搜索结果
    """
    logger.info(f"请求体: {request.model_dump_json()}")

    if request.class_name:
        # 如果 class_name 是java类的全名, 则获取类的简单名称
        # 如果 class_name 是java类的内部类, 则保留外层类名
        if "." in request.class_name:
            # 分割类名
            parts = request.class_name.split(".")
            if len(parts) > 1:
                # 检查是否是内部类（包含$符号）
                last_part = parts[-1]
    if "$" in last_part:
        # 内部类：保留外层类名，格式如 OuterClass$InnerClass
        outer_inner = last_part.split("$")
        if len(outer_inner) >= 2:
            request.class_name = outer_inner[0]
        else:
            request.class_name = last_part
    else:
        # 普通类：只保留简单类名
        request.class_name = last_part

    result = await github_service.search_source(request)

    return CommonResponse(body=result)

