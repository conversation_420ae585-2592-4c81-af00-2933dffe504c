import os
import logging
import logging.config

import yaml


def setup_logging() -> str:
    # 加载YAML配置文件
    with open('app/logging_config.yaml', 'r') as f:
        config = yaml.safe_load(f.read())

    # 确保日志目录存在
    log_path = config['handlers']['file']['filename']
    log_dir = os.path.dirname(log_path)
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 应用配置
    logging.config.dictConfig(config)
    return config
