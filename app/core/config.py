import asyncio
from contextlib import asynccontextmanager
import logging
import os
from typing import Optional, Dict

from fastapi import FastAPI
from pydantic import BaseModel

from app.core.apollo import ApolloSettings, ApolloSettingsConfigDict

__config__ = {
    "dev": "http://172.18.0.184:18080",
    "prod": "https://apollometazjk.aiyongtech.com",
}
__config_url__ = __config__[os.getenv("AY_PROFILE", "dev")]


class DifyKnowledge(BaseModel):
    """
    dify 知识库相关配置
    """

    DATASET_ID: str  # 数据集id
    QA_DOCUMENT_ID: str  # QA文档id
    TEXT_DOCUMENT_ID: str  # 段落文档id


class Base(ApolloSettings):
    model_config = ApolloSettingsConfigDict(
        config_server=__config_url__,
        appid="cn.loveapp.rag",
        cluster="dev",
        env_prefix="AY_",
    )


class AySettings(Base):
    __namespace__ = "application"
    __label__ = None
    SERVER_NAME: str = "aiyong_aigc_rag_service"
    # redis 配置
    REDDIS_HOST: Optional[str] = None
    REDDIS_PORT: Optional[int] = 6379
    REDDIS_PASSWORD: Optional[str] = None
    REDDIS_DB: Optional[int] = 0
    REDDIS_POOL_SIZE: Optional[int] = 5
    # 数据库 配置
    DATABASE_URL: Optional[str] = None
    DATABASE_POOL_SIZE: Optional[int] = 1
    DATABASE_POOL_OVERFLOW: Optional[int] = 10
    # rpc 配置
    RPC_UAC_URL: Optional[str] = None
    # API鉴权
    API_SUBMIT_TOKEN: Optional[str] = None
    # 飞书 配置
    FEISHU_APP_ID: str
    FEISHU_APP_SECRET: str
    FEISHU_WEBHOOK_URL: Optional[str] = "https://open.feishu.cn/open-apis/bot/v2/hook/"
    # OSS 配置
    OSS_ACCESS_KEY_ID: str
    OSS_ACCESS_KEY_SECRET: str
    OSS_ENDPOINT: str
    OSS_REGION: str
    # 爬虫配置
    CRAWL_TAOBAO_USERNAME: Optional[str] = None
    CRAWL_TAOBAO_PASSWORD: Optional[str] = None
    CRAWL_TEIM_SHIPPING_TIMEOUT: Optional[int] = 24 * 60 * 60
    CRAWL_PLAYWRIGHT_SERVER: Optional[str] = None
    CRAWL_PROXY: Optional[str] = None
    CRAWL_TIMEOUT: Optional[int] = 30 * 1000
    CRAWL_HEADLESS: Optional[bool] = True
    CRAWL_USER_AGENT: Optional[str] = (
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    )
    SESSION_KEY: Optional[str] = None
    # dify 插件配置
    DIFY_PLUGIN_SERVERLESS_URLS: Optional[Dict[str, str]] = None
    DIFY_PLUGIN_DAEMON_URL: Optional[str] = None
    # 告警分析配置
    GRAFANA_URL: Optional[str] = None
    GRAFANA_TOKEN: Optional[str] = None
    LOG_ES_URL: Optional[str] = None
    LOG_ES_USERNAME: Optional[str] = None
    LOG_ES_PASSWORD: Optional[str] = None
    GITHUB_TOKEN: Optional[str] = None


__settings: Optional[AySettings] = None

def settings() -> AySettings:
    """
    获取配置
    :return:
    """
    if not __settings:
        raise ValueError("settings not initialized")
    return __settings


def __init_settings():
    global __settings
    if not __settings:
        logging.info("start fetch apollo settings")
        ## start apollo 之前不能禁止使用 settings
        __settings = AySettings()
        server_name = __settings.SERVER_NAME
        logging.info(f"fetch apollo settings success {server_name}")



@asynccontextmanager
async def lifespan(app: FastAPI):
    await ApolloSettings.start()
    __init_settings()
    yield
    pass
    # await ApolloSettings.stop()
