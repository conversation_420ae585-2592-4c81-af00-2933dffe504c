# logging_config.yaml
version: 1
disable_existing_loggers: false

formatters:
    standard:
        format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    json:
        class:  pythonjsonlogger.jsonlogger.JsonFormatter
        format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

handlers:
    console:
        class: logging.StreamHandler
        level: INFO
        formatter: standard
        stream: ext://sys.stdout

    file:
        class: logging.handlers.RotatingFileHandler
        level: INFO
        formatter: json
        filename: logs/info.log
        maxBytes: 20971520  # 20MB
        backupCount: 1
        encoding: utf8

root:
    level: INFO
    handlers: [console, file]
