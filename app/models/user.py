import datetime
from typing import Optional

from sqlalchemy import String, TIMESTAMP, text
from sqlalchemy.dialects.mysql import INTEGER, TINYINT, VARCHAR
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    pass


class UserProductinfoTradeExt(Base):
    __tablename__ = 'user_productinfo_trade_ext'

    id: Mapped[int] = mapped_column(INTEGER(11), primary_key=True)
    seller_id: Mapped[Optional[str]] = mapped_column(String(80), comment='商家id')
    seller_nick: Mapped[Optional[str]] = mapped_column(VARCHAR(100), comment='用户nick')
    store_id: Mapped[Optional[str]] = mapped_column(String(30), comment='店铺id')
    corp_id: Mapped[Optional[str]] = mapped_column(String(50))
    db_status: Mapped[Optional[int]] = mapped_column(INTEGER(11), server_default=text("'0'"), comment='存单状态 ')
    pull_status: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='拉单状态')
    opened_app_names: Mapped[Optional[str]] = mapped_column(String(128))
    session_status: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='session状态')
    api_status: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='自有接口状态')
    top_status: Mapped[Optional[str]] = mapped_column(String(255), comment='第三方接口状态')
    db2search_status: Mapped[Optional[int]] = mapped_column(TINYINT(4),
                                                            comment='订单同步es状态,10同步完成，102同步中，-101同步失败')
    top_trade_count: Mapped[Optional[int]] = mapped_column(INTEGER(20), server_default=text("'0'"),
                                                           comment='入库时,淘宝近3个月订单数量')
    downgrade_tag: Mapped[Optional[str]] = mapped_column(String(255), comment='降级标')
    db_id: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='数据库ID')
    searchdb_id: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='搜索库id')
    list_id: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='表id')
    gray_level: Mapped[Optional[int]] = mapped_column(INTEGER(11), comment='灰度标识')
    pull_start_datetime: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)
    pull_end_datetime: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP)
    pull_end_point: Mapped[Optional[int]] = mapped_column(TINYINT(4), server_default=text("'0'"))
    gmt_modified: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, server_default=text(
        'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), comment='最后修改时间')
    gmt_create: Mapped[Optional[datetime.datetime]] = mapped_column(TIMESTAMP, comment='创建时间')
    member_id: Mapped[Optional[str]] = mapped_column(String(50), comment='1688会员ID')
