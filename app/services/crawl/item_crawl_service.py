import asyncio
import base64
import logging
import os
import random
import time
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import FastAP<PERSON>
from playwright.async_api import (
    <PERSON><PERSON><PERSON>,
    BrowserContext,
    Locator,
    Page,
    Playwright,
    ProxySettings,
    Request,
    async_playwright,
)
from python_ghost_cursor.playwright_async import create_cursor

from app.core.config import settings
from app.curd.crawl_dao import crawl_redis_dao
from app.util import aes


class ItemCrawlService:
    """
    淘宝商品信息爬虫服务
    """

    def __init__(self):
        self.__pw: Optional[Playwright] = None
        self.__browser: Optional[Browser] = None
        self.__storage_file: str = "./.storage/storage_state.json"
        self.__screenshots_dir: str = "./.screenshots"

    async def initialize(self):
        """
        初始化
        """
        if not settings().CRAWL_TAOBAO_USERNAME:
            return
        if not os.path.exists(self.__screenshots_dir):
            os.makedirs(self.__screenshots_dir, exist_ok=True)

        storage_dir = os.path.dirname(self.__storage_file)
        if not os.path.exists(storage_dir):
            os.makedirs(storage_dir, exist_ok=True)

        if not self.__pw:
            self.__pw = await async_playwright().start()

    async def __init_browser(self):
        if self.__browser and self.__browser.is_connected():
            return
        if settings().CRAWL_PLAYWRIGHT_SERVER:
            logging.info("使用远程浏览器")
            self.__browser = await self.__pw.chromium.connect(
                settings().CRAWL_PLAYWRIGHT_SERVER
            )
        else:
            logging.info("使用本地浏览器")
            self.__browser = await self.__pw.chromium.launch(
                headless=settings().CRAWL_HEADLESS,
                devtools=False,
                chromium_sandbox=False,
            )

        async def on_disconnected():
            logging.info("浏览器已关闭")
            self.__browser = None

        self.__browser.on("disconnected", on_disconnected)

    async def close(self):
        """
        关闭浏览器
        """
        if not self.__pw:
            return
        if self.__browser and self.__browser.is_connected():
            await self.__browser.close()
        self.__browser = None

        if self.__pw:
            await self.__pw.stop()
        self.__pw = None
        logging.info("关闭浏览器")

    async def crawl_item_shipping(self, item_id: str, sku_id: str):
        """
        爬取商品的发货运输信息
        :param item_id: 商品id
        :param sku_id: sku id
        :return: 发货运输信息
        """
        if not item_id:
            return None
        # 从缓存中获取
        shipping = await crawl_redis_dao.get_item_shipping(item_id, sku_id)
        if shipping:
            logging.info(f"{item_id} {sku_id} shipping 缓存命中: {shipping}")
            return shipping
        start = time.time()

        product_url = f"https://detail.tmall.com/item.htm?id={item_id}&skuId={sku_id}"

        await self.__init_browser()

        await self.__init_storage_state()

        proxy: ProxySettings = None
        if settings().CRAWL_PROXY:
            proxy = {"server": settings().CRAWL_PROXY}
        page = await self.__browser.new_page(
            storage_state=self.__storage_file,
            user_agent=settings().CRAWL_USER_AGENT,
            extra_http_headers={
                'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            },
            screen={
                "width": 1280,
                "height": 1080,
            },
            viewport={
                "width": 1280,
                "height": 1080,
            },
            proxy=proxy,
        )
        await page.add_init_script("delete Object.getPrototypeOf(navigator).webdriver")
        page.set_default_timeout(settings().CRAWL_TIMEOUT)

        async def on_request(req: Request):
            if req.resource_type == "document" and req.url == product_url:
                logging.info(f"请求: {req.url}, headers={req.headers}")

        page.on("request", on_request)
        try:
            await page.goto(product_url)
            # 等待页面加载完成
            await page.wait_for_selector("#purchasePanel,.J_MIDDLEWARE_FRAME_WIDGET")
            await self.__rest()
            # await page.pause()
            # 处理异常流量检测
            await self.__do_unusual_traffic(page)
            # 处理登录
            await self.__do_login(page)
            shipping_e = await page.wait_for_selector('[class*="shipping--"]')
            shipping = await shipping_e.inner_text()
            if shipping:
                # 设置缓存
                await crawl_redis_dao.set_item_shipping(item_id, sku_id, shipping)
                logging.info(f"{item_id} {sku_id} shipping 缓存设置成功: {shipping}")
            else:
                logging.info(f"{item_id} {sku_id} 未查询到 shipping: {shipping}")

            # 满足最低延迟, 防止被封
            min_delay = 3
            delay = int(time.time() - start)
            if delay < min_delay:
                await asyncio.sleep(random.randrange(min_delay - delay, min_delay))
            return shipping
        except Exception as e:
            try:
                await page.screenshot(
                    path=f"./.screenshots/{item_id}_{sku_id}.png", full_page=True
                )
            except Exception as ignore:
                pass
            raise e
        finally:
            await self.__save_storage_state(page)
            await page.close()

    async def __do_unusual_traffic(self, page: Page):
        """
        异常流量处理
        :param page: 页面对象
        :param timeout: 超时时间
        """
        e = page.locator(".J_MIDDLEWARE_FRAME_WIDGET > iframe")
        if e and await e.count() > 0:
            logging.info("需要网络验证")
            r = await self.__drag_verification_slider(
                selector=".J_MIDDLEWARE_FRAME_WIDGET > iframe", length=300, page=page
            )
            if not r:
                raise Exception("网络验证失败, 需要手动操作")

    async def __do_login(self, page: Page):
        """
        登录淘宝账号
        :param page: 页面对象
        :param loginBtn: 登录按钮
        """
        loginBtn = page.get_by_text("一键登录")
        if not loginBtn or await loginBtn.count() == 0:
            return
        logging.info(f"需要登录")
        await loginBtn.click()
        await page.wait_for_selector("#login-form")
        await self.__rest()
        # 用户名
        username = settings().CRAWL_TAOBAO_USERNAME
        for char in username:
            await page.locator("#fm-login-id").type(char)
            await asyncio.sleep(random.uniform(0.05, 0.2))
        await self.__rest()
        # 密码
        password = aes.decrypt(settings().SESSION_KEY, settings().CRAWL_TAOBAO_PASSWORD)
        for char in password:
            await page.locator("#fm-login-password").type(char)
            await asyncio.sleep(random.uniform(0.05, 0.2))
        await self.__rest()
        await page.get_by_role("button", name="登录").click()

        # #baxia-dialog-content = 登录的iframe
        # .dialog-btn.dialog-btn-feedback.primary = 记住账号密码的提示
        element = await page.wait_for_selector(
            "#baxia-dialog-content,.dialog-btn.dialog-btn-feedback.primary"
        )

        if element and await element.get_attribute("id") == "baxia-dialog-content":
            success = await self.__drag_verification_slider(
                selector="#baxia-dialog-content",
                length=350,
                page=page,
                wait_fun="""
                () => {
                    var f = document.getElementById("baxia-dialog-content");
                    if (f) {
                        var fd = f.contentDocument || f.contentWindow.document;
                        var r = fd.querySelector("#\\\\`nc_1_refresh1\\\\`");
                        if (r && r.style.display != 'none') {
                            return true;
                        }
                    }
                    var e = document.querySelector(".dialog-btn.dialog-btn-feedback.primary");
                    return e && e.style.display != 'none';
                }
                """,
            )
            if not success:
                raise Exception("登录失败，请手动操作")

        button = page.get_by_role("button", name="知道了")
        if button and await button.is_visible():
            await self.__rest(0.5)
            await button.click()
            await self.__rest(0.5)

        logging.info("登录成功")

    async def __drag_verification_slider(
        self,
        selector: str,
        length: int,
        page: Page,
        wait_fun: str = None,
        retry: int = 1,
    ) -> bool:
        """
        拖动验证滑块
        :param page: 页面对象
        :param retry: 重试次数
        :return: 是否成功
        """
        if retry > 3:
            logging.info("重试次数过多")
            return False
        logging.info(f"第 {retry} 次尝试")
        content_frame = page.locator(selector).content_frame
        element = content_frame.get_by_role("button", name="滑块")
        await element.wait_for()
        if not element or await element.is_visible() == False:
            logging.info("获取不到滑块")
            return False
        box = await element.bounding_box()
        if not box:
            logging.info("获取位置出错")
            return False

        await element.hover()
        s_x = box["x"] + box["width"] / 2 + random.randint(1, 3)
        s_y = box["y"] + box["height"] / 2 + random.randint(-3, 3)
        await page.mouse.move(s_x, s_y)
        await page.mouse.down()

        e_x = box["x"] - box["width"] / 2 + length + 50
        e_y = box["y"] + box["height"] / 2
        cursor = create_cursor(page)
        await cursor.move(element)
        await cursor.move_to({"x": e_x, "y": e_y})
        await page.mouse.up()
        await self.__rest(0.5)
        if wait_fun:
            await page.wait_for_function(wait_fun)
        else:
            f = page.locator(selector)
            if not f or await f.count() == 0:
                return True
        element = content_frame.locator("#\\`nc_1_refresh1\\`")
        if element and await element.count() > 0 and await element.is_visible():
            await element.hover()
            await element.click()
            await self.__rest(0.5 * retry)
            return await self.__drag_verification_slider(
                selector=selector,
                page=page,
                length=length,
                wait_fun=wait_fun,
                retry=retry + 1,
            )
        else:
            return True

    async def __rest(self, timeout: float = 0.4):
        """
        延迟一小会儿
        :param timeout: 时间
        """
        await asyncio.sleep(random.random() / 2 + timeout)

    async def __save_storage_state(self, page: Page) -> str:
        await page.context.storage_state(path=self.__storage_file)
        # 读取存储状态
        with open(self.__storage_file, "r") as f:
            storage_state = f.read()
            await crawl_redis_dao.set_storage_state(
                settings().CRAWL_TAOBAO_USERNAME, storage_state
            )

    async def __init_storage_state(self) -> str:
        if not os.path.exists(self.__storage_file):
            storage_state = await crawl_redis_dao.get_storage_state(
                settings().CRAWL_TAOBAO_USERNAME
            )
            if storage_state:
                logging.info("从缓存获取到存储状态")
                with open(self.__storage_file, "w") as f:
                    f.write(storage_state)
            else:
                logging.info("缓存中没有存储状态, 创建新的存储状态")
                with open(self.__storage_file, "w") as f:
                    f.write("{}")


item_crawl_service = ItemCrawlService()


@asynccontextmanager
async def lifespan(app: FastAPI):
    await item_crawl_service.initialize()
    yield
    await item_crawl_service.close()
