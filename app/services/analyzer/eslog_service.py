import logging
import re
from collections import Counter
from typing import Any, Dict, List, Optional

from app.curd.es_dao import es_dao
from app.schemas.analyzer import ErrorInfo, ExceptionDetail

logger = logging.getLogger(__name__)


class EsLogService:
    """
    ES日志查询服务
    """

    def __init__(self):
        pass

    async def query_by_timestamp_range(
        self, query: str, index: str, time_range_minutes: int
    ) -> List[Dict]:
        """
        根据时间范围查询ES日志
        """
        return await es_dao.query_by_timestamp_range(query, index, time_range_minutes)

    async def get_by_id(self, index: str, doc_id: str) -> Optional[Dict]:
        """
        根据ID查询单条日志记录

        Args:
            index: ES索引名称
            doc_id: 文档ID

        Returns:
            Optional[Dict]: 日志记录内容，如果未找到则返回None
        """
        return await es_dao.query_by_id(index, doc_id)

    async def query(
        self,
        index: str,
        query_string: str,
        timestamp: str
    ) -> List[Dict]:
        """
        根据时间戳和查询字符串查询日志记录

        Args:
            index: ES索引名称
            query_string: 查询字符串
            timestamp: 时间戳（ISO 8601格式）

        Returns:
            List[Dict]: 日志记录列表
        """
        return await es_dao.query_by_timestamp_and_query_string(
            index,
            query_string,
            timestamp
        )

    def extract_errors_and_exceptions(
        self, es_results: List[Dict], top_n: int
    ) -> List[ErrorInfo]:
        """
        抽取日志中的错误和异常信息，并返回错误信息列表

        Args:
            es_results: ES日志查询结果
        Returns:
            List[ErrorInfo]: 错误信息列表
        """
        error_counter = Counter()
        error_exception_details_group = {}
        error_es_info_group = {}

        for record in es_results:
            message = record.get("message", "")
            stack_trace = record.get("stack_trace", "")
            index = record.get("_index", "")
            id = record.get("_id", "")
            timestamp = record.get("@timestamp", "")

            if message:
                # 提取错误信息的关键部分
                error_key = self._extract_error_key(message)
                if error_key:
                    error_counter[error_key] += 1

                    error_es_info_group[error_key] = {
                        "index": index,
                        "id": id,
                        "timestamp": timestamp,
                    }

                    # 如果有堆栈信息，提取详细的异常信息
                    if stack_trace:
                        exception_info = self._extract_cn_loveapp_class(stack_trace)
                        if exception_info:
                            if error_key not in error_exception_details_group:
                                # 创建新的异常详情
                                error_exception_details_group[error_key] = ExceptionDetail(
                                    class_name=exception_info["class_name"],
                                    method_name=exception_info["method_name"],
                                    line_number=exception_info["line_number"],
                                    exception_class=exception_info.get(
                                        "exception_class"
                                    ),
                                )

        # 获取前5个最常见的错误
        top_error_keys = error_counter.most_common(top_n)

        errors = []
        # 如果有异常详情，按异常详情分组合并
        if error_exception_details_group:
            # 按异常详情分组
            grouped_errors = {}
            for error_key, count in top_error_keys:
                exception_detail = error_exception_details_group.get(error_key)
                if exception_detail:
                    # 使用异常详情的关键信息作为分组键
                    group_key = (
                        exception_detail.class_name,
                        exception_detail.method_name,
                        exception_detail.line_number,
                        exception_detail.exception_class,
                    )

                    if group_key not in grouped_errors:
                        grouped_errors[group_key] = {
                            "error_keys": [],
                            "total_count": 0,
                            "exception_detail": exception_detail,
                        }

                    grouped_errors[group_key]["error_keys"].append((error_key, count))
                    grouped_errors[group_key]["total_count"] += count

            # 为每个分组创建ErrorInfo
            for group_info in grouped_errors.values():
                # 选择count最大的error作为代表
                representative_error_key = max(group_info["error_keys"], key=lambda x: x[1])[0]
                es_info = error_es_info_group.get(representative_error_key)

                error_info = ErrorInfo(
                    error=representative_error_key,
                    count=group_info["total_count"],
                    exception_details=group_info["exception_detail"],
                    index=es_info.get("index"),
                    id=es_info.get("id"),
                    timestamp=es_info.get("timestamp"),
                )
                errors.append(error_info)

            # 处理没有异常详情的错误
            for error_key, count in top_error_keys:
                if error_key not in error_exception_details_group:
                    es_info = error_es_info_group.get(error_key)
                    error_info = ErrorInfo(
                        error=error_key,
                        count=count,
                        index=es_info.get("index"),
                        id=es_info.get("id"),
                        timestamp=es_info.get("timestamp"),
                        exception_details=None,
                    )
                    errors.append(error_info)
        else:
            # 没有异常详情时，按原逻辑处理
            for error_key, count in top_error_keys:
                es_info = error_es_info_group.get(error_key)
                error_info = ErrorInfo(
                    error=error_key,
                    count=count,
                    index=es_info.get("index"),
                    id=es_info.get("id"),
                    timestamp=es_info.get("timestamp"),
                    exception_details=error_exception_details_group.get(error_key),
                )
                errors.append(error_info)

        return errors

    def _extract_error_key(self, message: str) -> Optional[str]:
        """从错误消息中提取关键错误信息"""
        if not message:
            return None

        # 找到所有左中括号的位置
        right_bracket_positions = []
        for i, char in enumerate(message):
            if char == "]":
                right_bracket_positions.append(i)

        # 如果有第三个左中括号，从第三个左中括号后的第一个左中括号开始到最后一个右中括号的内容
        if len(right_bracket_positions) >= 3:
            # 从第三个左中括号之后开始查找下一个左中括号
            third_bracket_pos = right_bracket_positions[2]
            next_left_bracket_pos = message.find("[", third_bracket_pos + 1)

            if next_left_bracket_pos != -1:
                # 找到最后一个右中括号的位置
                last_right_bracket_pos = message.rfind("]")

                if last_right_bracket_pos > next_left_bracket_pos:
                    # 提取第三个左中括号后的第一个左中括号到最后一个右中括号之间的内容（不包括中括号本身）
                    content = message[
                        next_left_bracket_pos + 1 : last_right_bracket_pos
                    ].strip()
                    if content and content != "-":
                        return content

        # 如果超过500字符，只返回前500字符加省略号
        stripped_message = message.strip()
        if len(stripped_message) > 500:
            return stripped_message[:500] + "..."
        return stripped_message

    def _extract_cn_loveapp_class(self, stack_trace: str) -> Optional[Dict[str, Any]]:
        """从堆栈信息中提取第一个cn.loveapp包下的类及其方法和行号，如果找不到则返回第一个类的信息"""
        if not stack_trace:
            return None

        # 提取异常类名（通常在堆栈跟踪的第一行）
        exception_class = self._extract_exception_class(stack_trace)

        # 首先查找cn.loveapp包下的类，包含方法名和行号
        cn_loveapp_pattern = (
            r"at\s+(cn\.loveapp\.[^\s\(]+)\.([a-zA-Z_][a-zA-Z0-9_]*)\([^:]*:(\d+)\)"
        )
        cn_loveapp_matches = re.findall(cn_loveapp_pattern, stack_trace)

        if cn_loveapp_matches:
            # 返回第一个匹配的cn.loveapp类的详细信息
            class_name, method_name, line_number = cn_loveapp_matches[0]
            return {
                "class_name": class_name,
                "method_name": method_name,
                "line_number": int(line_number),
                "exception_class": exception_class,
            }

        # 如果没有找到cn.loveapp包下的类，查找第一个类的详细信息
        general_pattern = r"at\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\.([a-zA-Z_][a-zA-Z0-9_]*)\([^:]*:(\d+)\)"
        general_matches = re.findall(general_pattern, stack_trace)

        if general_matches:
            # 返回第一个匹配的类的详细信息
            class_name, method_name, line_number = general_matches[0]
            return {
                "class_name": class_name,
                "method_name": method_name,
                "line_number": int(line_number),
                "exception_class": exception_class,
            }

        # 如果上面的模式都没匹配到，尝试更宽松的模式（可能没有行号）
        fallback_pattern = r"at\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\.([a-zA-Z_][a-zA-Z0-9_]*)\("
        fallback_matches = re.findall(fallback_pattern, stack_trace)

        if fallback_matches:
            class_name, method_name = fallback_matches[0]
            return {
                "class_name": class_name,
                "method_name": method_name,
                "line_number": None,
                "exception_class": exception_class,
            }

        return None

    def _extract_exception_class(self, stack_trace: str) -> Optional[str]:
        """从堆栈跟踪中提取异常类名"""
        if not stack_trace:
            return None

        # 异常类名通常在堆栈跟踪的第一行
        # 常见格式：
        # java.lang.NullPointerException: ...
        # Exception in thread "main" java.lang.RuntimeException: ...
        # com.example.CustomException: ...

        lines = stack_trace.strip().split("\n")
        if not lines:
            return None

        first_line = lines[0].strip()
        if ":" in first_line:
            return first_line.split(":")[0]

        return None


eslog_service = EsLogService()
