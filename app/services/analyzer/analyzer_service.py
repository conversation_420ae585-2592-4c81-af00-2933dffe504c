import logging
import traceback

from app.core.config import settings
from app.external_services.feishu.feishu_webhook_service import feishu_webhook_service
from app.schemas.analyzer import GrafanaRequest, GrafanaAlertAnalysisResponse
from app.services.analyzer.analyzers.log_alert_analyzer import GrafanaLogAlertAnalyzer

logger = logging.getLogger(__name__)


class AnalyzerService:
    """
    分析服务
    """

    def __init__(self):
        self.__log_analyzer = None

    async def handle_grafana_callback(
        self, hookId: str, request: GrafanaRequest, time_range_minutes: int = 15
    ):
        """
        分析Grafana告警链接

        Args:
            request: GrafanaRequest grafana 告警回调请求体
            time_range_minutes: 查询时间范围

        Returns:
            包含分析结果的字典
        """
        try:
            # 参数验证
            if not request.rule_url or not isinstance(request.rule_url, str):
                logger.info("告警链接不能为空")
                return

            # 创建分析器实例
            self.__create_log_analyzer()

            # 执行分析
            analysis_result = None
            if "[Alerting]" in request.title:
                result = await self.__log_analyzer.analyze_log_alert(
                    request.rule_url, time_range_minutes, False
                )
                analysis_result = result
            else:
                analysis_result = GrafanaAlertAnalysisResponse(
                    grafana_alert_link=request.rule_url,
                    kibana_log_link=None,
                    analysis_result=None,
                    error=None,
                    message=None,
                )

            # 格式化为markdown
            markdown_content = (
                feishu_webhook_service.format_analysis_result_to_markdown(
                    request, analysis_result
                )
            )

            # 发送到飞书
            title = request.title
            await feishu_webhook_service.send_markdown_message(
                hookId, title, markdown_content
            )

        except Exception as e:
            error_msg = traceback.format_exc()
            logger.error(f"处理grafana告警回调时发生错误: {e}\n{error_msg}")

    def __create_log_analyzer(self):
        if not self.__log_analyzer:
            grafana_url = settings().GRAFANA_URL
            grafana_token = settings().GRAFANA_TOKEN
            self.__log_analyzer = GrafanaLogAlertAnalyzer(
                grafana_url=grafana_url, grafana_token=grafana_token
            )


# 创建服务实例
analyzer_service = AnalyzerService()
