import asyncio
import json
import logging
from typing import Dict, Optional
from urllib.parse import parse_qs, urlparse

import httpx

from app.schemas.analyzer import AnalysisResult, GrafanaAlertAnalysisResponse, GitHubSearchRequest
from app.services.analyzer.eslog_service import eslog_service
from app.services.analyzer.github_service import github_service

logger = logging.getLogger(__name__)


class GrafanaLogAlertAnalyzer:
    """Grafana 日志告警分析工具"""

    def __init__(
        self,
        grafana_url: str,
        grafana_token: str,
    ):
        """
        初始化分析器

        Args:
            grafana_url: Grafana服务器URL
            grafana_token: Grafana API Token
        """
        self.grafana_url = grafana_url.rstrip("/")
        self.grafana_headers = {
            "Authorization": f"Bearer {grafana_token}",
            "Content-Type": "application/json",
        }

    async def analyze_log_alert(
        self, alert_link: str, time_range_minutes: int, fetch_source_content
    ) -> GrafanaAlertAnalysisResponse:
        """
        分析 Grafana 日志告警

        Args:
            alert_link: Grafana 日志告警链接
            time_range_minutes: 查询时间范围（分钟）
            fetch_source_content: 是否获取源码内容

        Returns:
            分析结果的Pydantic模型对象
        """
        try:
            # 1. 解析链接获取dashboard ID和alert ID
            dashboard_id, alert_id = self._parse_alert_link(alert_link)
            if not dashboard_id or not alert_id:
                return GrafanaAlertAnalysisResponse(
                    error="无法解析告警链接中的dashboard ID或alert ID"
                )

            # 2. 获取dashboard配置
            dashboard_config = await self._get_dashboard_config(dashboard_id)
            if not dashboard_config:
                return GrafanaAlertAnalysisResponse(error="无法获取dashboard配置")

            # 3. 检查dashboard标题是否包含"日志"
            dashboard_title = dashboard_config.get("dashboard", {}).get("title", "")
            if "日志" not in dashboard_title:
                return GrafanaAlertAnalysisResponse(
                    message="dashboard标题不包含'日志'，跳过分析"
                )

            # 4. 找出对应的alert查询语句和ES索引
            es_query, datasource_name, kibana_log_link = (
                self._get_es_query_and_datasource_from_dashboard_config(
                    dashboard_config, alert_id
                )
            )
            if not es_query or not datasource_name:
                return GrafanaAlertAnalysisResponse(error="无法找到对应的ES查询语句")

            # 5. 从数据源配置中获取ES索引
            es_index = await self._get_es_index_from_datasource(datasource_name)

            # 6. 查询ES获取最新数据
            es_results = await eslog_service.query_by_timestamp_range(
                es_query, es_index, time_range_minutes
            )
            if not es_results:
                return GrafanaAlertAnalysisResponse(error="ES查询无结果")

            # 7. 分析错误和异常
            errors = eslog_service.extract_errors_and_exceptions(es_results, top_n=3)

            analysis_result = AnalysisResult(errors=errors)

            # 8. 对于cn.loveapp包下的类，获取GitHub源码
            if errors:
                is_dev_branch = es_index.startswith("pretest")
                analysis_result = await self._enrich_with_github_source(
                    is_dev_branch, analysis_result, fetch_source_content
                )

            return GrafanaAlertAnalysisResponse(
                grafana_alert_link=alert_link,
                kibana_log_link=kibana_log_link,
                analysis_result=analysis_result,
            )

        except Exception as e:
            logger.error(f"分析告警链接时发生错误: {str(e)}")
            return GrafanaAlertAnalysisResponse(error=f"分析过程中发生错误: {str(e)}")

    def _parse_alert_link(self, alert_link: str) -> tuple:
        """解析告警链接获取dashboard ID和alert ID"""
        try:
            parsed_url = urlparse(alert_link)

            # 从路径中提取dashboard ID
            path_parts = parsed_url.path.split("/")
            dashboard_id = None
            for i, part in enumerate(path_parts):
                if part == "d" and i + 1 < len(path_parts):
                    dashboard_id = path_parts[i + 1].split("-")[0]  # 取ID部分
                    break

            # 从查询参数中提取alert ID
            query_params = parse_qs(parsed_url.query)
            alert_id = None

            # 尝试从不同的参数中获取alert ID
            for param in ["alertId", "alert_id", "panelId", "panel_id"]:
                if param in query_params:
                    alert_id = query_params[param][0]
                    break

            return dashboard_id, alert_id

        except Exception as e:
            logger.error(f"解析告警链接失败: {str(e)}")
            return None, None

    async def _get_dashboard_config(self, dashboard_id: str) -> Optional[Dict]:
        """获取dashboard配置"""
        try:
            url = f"{self.grafana_url}/api/dashboards/uid/{dashboard_id}"
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url, headers=self.grafana_headers, timeout=30
                )

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取dashboard配置失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"请求dashboard配置时发生错误: {str(e)}")
            return None

    def _get_es_query_and_datasource_from_dashboard_config(
        self, dashboard_config: Dict, alert_id: str
    ) -> tuple[Optional[str], Optional[str], Optional[str]]:
        """从dashboard配置中提取ES查询语句、数据源名称和Kibana链接"""
        try:
            panels = dashboard_config.get("dashboard", {}).get("panels", [])

            for panel in panels:
                # 检查panel ID是否匹配
                if str(panel.get("id")) == str(alert_id):
                    # 获取数据源信息 - datasource字段可能是字符串（数据源名称）或对象
                    datasource = panel.get("datasource", "")

                    # 获取第一个link作为Kibana链接
                    kibana_log_link = None
                    links = panel.get("links", [])
                    if links and len(links) > 0:
                        kibana_log_link = links[0].get("url", "")

                    # 查找ES查询
                    targets = panel.get("targets", [])
                    for target in targets:
                        # 从target中提取查询信息
                        query = target.get("query", "")
                        if "$" in query:
                            continue
                        return query, datasource, kibana_log_link
            return None, None, None

        except Exception as e:
            logger.error(f"提取ES查询语句和索引时发生错误: {str(e)}")
            return None, None, None

    async def _get_es_index_from_datasource(
        self, datasource_name: str
    ) -> Optional[str]:
        """通过数据源名称获取ES索引"""
        try:
            # 使用Grafana API获取数据源配置
            url = f"{self.grafana_url}/api/datasources/name/{datasource_name}"
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url, headers=self.grafana_headers, timeout=30
                )

            if response.status_code == 200:
                datasource_config = response.json()
                logger.info(f"获取到数据源 {datasource_name} 的配置")

                # 检查数据源类型
                datasource_type = datasource_config.get("type", "")
                if "elasticsearch" not in datasource_type.lower():
                    logger.warning(
                        f"数据源 {datasource_name} 不是Elasticsearch类型: {datasource_type}"
                    )
                    return None

                # 从数据源配置中提取索引信息
                # 不同的ES插件可能有不同的字段名
                index_fields = [
                    "database",  # 最常见字段，通常存储索引模式
                    "index",
                    "indexName",
                    "pattern",
                    "indexPattern",
                    "defaultIndex",
                ]

                # 首先检查主配置
                for field in index_fields:
                    if field in datasource_config and datasource_config[field]:
                        logger.info(
                            f"从数据源 {datasource_name} 的 {field} 字段获取到索引: {datasource_config[field]}"
                        )
                        return datasource_config[field]

                # 检查jsonData中的配置（ES数据源的主要配置通常在这里）
                json_data = datasource_config.get("jsonData", {})
                if json_data:
                    logger.debug(f"数据源 {datasource_name} 的jsonData: {json_data}")

                    for field in index_fields:
                        if field in json_data and json_data[field]:
                            logger.info(
                                f"从数据源 {datasource_name} 的jsonData.{field}获取到索引: {json_data[field]}"
                            )
                            return json_data[field]

                    # 检查一些特殊的ES配置字段
                    es_specific_fields = [
                        "esVersion",  # 可能包含版本和索引信息
                        "timeField",  # 时间字段，可能暗示索引
                        "interval",  # 索引间隔
                        "logLevelField",
                        "logMessageField",
                    ]

                    for field in es_specific_fields:
                        if field in json_data:
                            logger.debug(
                                f"数据源 {datasource_name} 包含ES字段 {field}: {json_data[field]}"
                            )

                # 如果还是没找到，尝试从URL中提取（某些配置可能在URL中）
                url_field = datasource_config.get("url", "")
                if url_field:
                    logger.debug(f"数据源 {datasource_name} 的URL: {url_field}")
                    # 可以尝试从URL路径中提取索引信息，但这通常不包含索引

                logger.warning(
                    f"数据源 {datasource_name} 配置中未找到索引信息，可能需要在查询中指定"
                )
                logger.debug(
                    f"完整的数据源配置: {json.dumps(datasource_config, indent=2, ensure_ascii=False)}"
                )
                return None
            else:
                logger.error(
                    f"获取数据源配置失败: {response.status_code}, {response.text}"
                )
                return None

        except Exception as e:
            logger.error(f"通过数据源名称获取索引时发生错误: {str(e)}")
            return None

    async def _enrich_with_github_source(
        self,
        is_dev_branch: bool,
        analysis_result: AnalysisResult,
        fetch_source_content: bool,
    ) -> AnalysisResult:
        """为cn.loveapp包下的类获取GitHub源码"""
        try:
            if not analysis_result.errors:
                return analysis_result

            # 收集所有需要搜索的任务
            search_tasks = []
            for error_info in analysis_result.errors:
                if error_info.exception_details:
                    detail = error_info.exception_details
                    class_name = detail.class_name or ""
                    method_name = detail.method_name or ""
                    line_number = detail.line_number

                    # 只处理cn.loveapp包下的类
                    if class_name.startswith("cn.loveapp"):
                        # 提取简单类名
                        simple_class_name = class_name.split(".")[-1]
                        search_tasks.append(
                            (
                                detail,
                                is_dev_branch,
                                simple_class_name,
                                method_name,
                                line_number,
                            )
                        )

            # 并发执行所有GitHub源码搜索
            if search_tasks:
                github_tasks = [
                    github_service.search_source(
                        GitHubSearchRequest(
                            is_dev_branch=is_dev_branch,
                            class_name=simple_class_name,
                            method_name=method_name,
                            line_number=line_number,
                            fetch_source_content=fetch_source_content,
                        )
                    )
                    for _, is_dev_branch, simple_class_name, method_name, line_number in search_tasks
                ]

                results = await asyncio.gather(*github_tasks, return_exceptions=True)

                # 将结果分配给对应的detail
                for i, (detail, _, _, _, _) in enumerate(search_tasks):
                    result = results[i]
                    if not isinstance(result, Exception) and result:
                        detail.github_source = result

            return analysis_result

        except Exception as e:
            logger.error(f"获取GitHub源码时发生错误: {str(e)}")
            return analysis_result
