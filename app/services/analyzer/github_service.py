import base64
import logging
import re
from typing import Optional

import httpx

from app.core.config import settings
from app.schemas.analyzer import GitHubSourceInfo, GitHubSearchRequest

logger = logging.getLogger(__name__)


class GithubService:
    """
    GitHub搜索服务
    """

    def __init__(self):
        pass

    async def search_source(
        self,
        request: GitHubSearchRequest,
    ) -> Optional[GitHubSourceInfo]:
        """
        通过GitHub API搜索源码，返回第一个匹配的源码文件

        Args:
            request: GitHub搜索请求参数
        """
        try:
            # GitHub搜索API
            search_url = "https://api.github.com/search/code"

            # 构建搜索查询：在aiyongbao组织下搜索包含类名和方法名的Java文件
            query = f"class {request.class_name} {request.method_name} org:aiyongbao language:java"

            params = {
                "q": query,
                "sort": "indexed",
                "order": "desc",
                "per_page": 1,  # 限制结果数量
            }

            headers = self.__headers()

            logger.info(f"搜索GitHub源码: {query}")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    search_url, params=params, headers=headers, timeout=30
                )

            if response.status_code == 200:
                result = response.json()
                items = result.get("items", [])

                if items:
                    # 取第一个匹配的文件
                    first_item = items[0]
                    file_content = None
                    if request.line_number is not None and request.fetch_source_content:
                        # 获取文件内容
                        file_content = await self.get_file_content(
                            request.is_dev_branch,
                            first_item.get("url", ""),
                            request.method_name,
                            request.line_number,
                        )

                    return GitHubSourceInfo(
                        file_name=first_item.get("name", ""),
                        file_path=first_item.get("path", ""),
                        repository=first_item.get("repository", {}).get(
                            "full_name", ""
                        ),
                        html_url=first_item.get("html_url", ""),
                        file_content=file_content,
                    )
                else:
                    logger.info(f"未找到匹配的源码文件: {query}")
                    return None

            elif response.status_code == 403:
                logger.warning("GitHub API访问受限，可能需要认证token或已达到速率限制")
                return None
            else:
                logger.error(f"GitHub搜索失败: {response.status_code}, {response.text}")
                return None

        except Exception as e:
            logger.error(f"搜索GitHub源码时发生错误: {str(e)}")
            return None

    def __headers(self):
        headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "GrafanaAlertAnalyzer/1.0",
        }
        # 如果有GitHub token，添加认证头
        github_token = settings().GITHUB_TOKEN
        if github_token:
            headers["Authorization"] = f"token {github_token}"

        return headers

    async def get_file_content(
        self,
        is_dev_branch: bool,
        file_url: str,
        method_name: str | None,
        line_number: int | None,
    ) -> Optional[str]:
        """
        获取GitHub文件内容，优化截取异常方法对应的上下文

        Args:
            is_dev_branch: 是否是 dev 分支 
            file_url: 文件URL
            method_name: 方法名
            line_number: 行号
        """
        try:

            if not file_url:
                return None
            original_file_url = file_url
            # 检查是否需要从dev分支获取文件
            if is_dev_branch:

                # GitHub Code Search API返回的URL格式:
                # https://api.github.com/repositories/369411179/contents/path?ref=commit_hash
                # 需要将ref参数从commit_hash改为dev分支
                if "?ref=" in file_url:
                    # 替换现有的ref参数为dev分支
                    base_url = file_url.split("?ref=")[0]
                    file_url = f"{base_url}?ref=dev"
                    logger.info(f"已将URL修改为dev分支: {file_url}")
                elif "?" in file_url:
                    # 如果有其他查询参数但没有ref，添加ref=dev
                    file_url = file_url + "&ref=dev"
                else:
                    # 如果没有查询参数，添加ref=dev
                    file_url = file_url + "?ref=dev"

                logger.info(f"检测到pretest索引，将从dev分支获取源码: {file_url}")

            headers = self.__headers()
            async with httpx.AsyncClient() as client:
                response = await client.get(file_url, headers=headers, timeout=30)

            # 如果从dev分支获取失败且当前使用的是dev分支，尝试使用原始URL
            if response.status_code != 200 and is_dev_branch:
                logger.info(f"使用原始URL重试: {original_file_url}")
                async with httpx.AsyncClient() as client:
                    response = await client.get(
                        original_file_url, headers=headers, timeout=30
                    )

            if response.status_code == 200:
                file_data = response.json()
                content = file_data.get("content", "")

                if content:
                    # GitHub API返回的内容是base64编码的
                    decoded_content = base64.b64decode(content).decode("utf-8")

                    # 如果有方法名或异常行号，尝试找到位置并截取上下文
                    context_chars = 5000
                    if method_name or line_number:
                        optimized_content = self._extract_method_context(
                            decoded_content,
                            method_name,
                            line_number,
                            context_chars=context_chars,
                        )
                        if optimized_content:
                            return optimized_content

                    if len(decoded_content) > context_chars:
                        decoded_content = (
                            decoded_content[:context_chars] + "\n... (内容已截断)"
                        )

                    return decoded_content
                else:
                    return None
            else:
                logger.error(f"获取GitHub文件内容失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取GitHub文件内容时发生错误: {str(e)}")
            return None

    def _extract_method_context(
        self,
        file_content: str,
        method_name: str | None,
        line_number: int | None,
        context_chars: int,
    ) -> Optional[str]:
        """从文件内容中提取指定方法或异常行的上下文"""
        try:
            lines = file_content.split("\n")
            target_line_index = None

            # 优先使用异常行号，如果没有则查找方法定义
            if line_number is not None:
                # 异常行号是从1开始的，转换为0基索引
                target_line_index = line_number - 1
                if 0 <= target_line_index < len(lines):
                    logger.info(f"使用异常行号 {line_number} 作为目标行")
                else:
                    logger.warning(
                        f"异常行号 {line_number} 超出文件范围，文件共 {len(lines)} 行"
                    )
                    target_line_index = None

            # 如果没有有效的异常行号，尝试查找方法定义
            if target_line_index is None and method_name:
                for i, line in enumerate(lines):
                    # 匹配方法定义的模式
                    # 支持各种访问修饰符和返回类型
                    method_patterns = [
                        rf"\b{re.escape(method_name)}\s*\(",  # 基本方法名匹配
                        rf"(public|private|protected|static|final|synchronized|\s)+.*\b{re.escape(method_name)}\s*\(",  # 带修饰符的方法
                        rf"^\s*(public|private|protected)?\s*(static)?\s*(final)?\s*\w+\s+{re.escape(method_name)}\s*\(",  # 标准方法定义
                    ]

                    for pattern in method_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            target_line_index = i
                            logger.info(
                                f"找到方法 {method_name} 在第 {i+1} 行: {line.strip()}"
                            )
                            break

                    if target_line_index is not None:
                        break

            if target_line_index is None:
                logger.info(
                    f"未找到目标行（异常行号: {line_number}, 方法名: {method_name}），使用默认截取策略"
                )
                return None

            # 先为所有行添加行号
            numbered_lines = []
            for i, line in enumerate(lines):
                numbered_lines.append(f"{i+1:4d}: {line}")

            # 计算需要截取的行数范围
            # 先估算每行平均字符数（包含行号）
            sample_line = numbered_lines[0] if numbered_lines else "    1: "
            avg_chars_per_line = len(sample_line) + 50  # 估算平均行长度

            # 估算需要的行数
            estimated_lines_needed = int(context_chars / avg_chars_per_line)
            lines_before = estimated_lines_needed // 2
            lines_after = estimated_lines_needed // 2

            # 确定起始和结束行
            start_line = max(0, target_line_index - lines_before)
            end_line = min(len(numbered_lines), target_line_index + lines_after + 1)

            # 提取上下文行（已包含行号）
            context_lines = numbered_lines[start_line:end_line]
            context_content = "\n".join(context_lines)

            # 如果内容仍然太长，进行精确截取
            if len(context_content) > context_chars:
                # 找到目标行在上下文中的位置
                target_line_in_context = target_line_index - start_line

                # 计算目标行的字符位置
                chars_before_target = sum(
                    len(line) + 1 for line in context_lines[:target_line_in_context]
                )

                # 计算截取范围
                start_char = max(0, chars_before_target - context_chars // 2)
                end_char = min(len(context_content), start_char + context_chars)

                # 调整起始位置，确保不在行中间截断
                if start_char > 0:
                    # 向前找到行的开始
                    while start_char > 0 and context_content[start_char - 1] != "\n":
                        start_char -= 1

                # 调整结束位置
                if end_char < len(context_content):
                    # 向后找到行的结束
                    while (
                        end_char < len(context_content)
                        and context_content[end_char] != "\n"
                    ):
                        end_char += 1

                context_content = context_content[start_char:end_char]

                # 添加截断提示
                if start_char > 0:
                    context_content = "... (上方内容已截断)\n" + context_content
                if end_char < len("\n".join(numbered_lines)):
                    context_content = context_content + "\n... (下方内容已截断)"

            return context_content

        except Exception as e:
            logger.error(f"提取方法上下文时发生错误: {str(e)}")
            return None


github_service = GithubService()
