import logging
from typing import Type, TypeVar, Optional

import requests
from pydantic import BaseModel
from starlette.exceptions import HTTPException

from app.schemas.base import CommonResponse

T = TypeVar('T', bound=BaseModel)


def request(url: str, req: BaseModel, response_type: Type[T]) -> T:
    response = requests.post(url, json=req.model_dump())
    if response.status_code != 200:
        raise HTTPException(status_code=response.status_code, detail=f'Failed to {response.request.path_url}')
    result = response.json()
    resp = CommonResponse[response_type].model_validate(result)
    return resp.body if resp.body else None


def request_without_common_response(url: str, req: BaseModel, response_type: Type[T], headers: Optional[dict[str, str]] = None) -> T:
    logging.info(f'发送请求 url:[{url} headers:[{headers}] body:[{req.model_dump_json()}]]')
    response = requests.post(url, json=req.model_dump(), headers=headers)
    logging.info(f'收到响应 url:[{url} body:[{response.json()}]]')
    if response.status_code != 200:
        raise HTTPException(status_code=response.status_code, detail=f'Failed to {response.request.path_url}')
    return response.json()
