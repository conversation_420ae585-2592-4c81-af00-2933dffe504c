import json
import logging

import httpx

from app.core.config import settings
from app.schemas.analyzer import GrafanaAlertAnalysisResponse, GrafanaRequest

logger = logging.getLogger(__name__)


class FeishuWebhookService:
    """飞书自定义机器人服务"""

    async def send_markdown_message(
        self, hookId: str, title: str, content: str
    ) -> bool:
        """
        发送markdown格式的消息到飞书群

        Args:
            title: 消息标题
            content: markdown格式的消息内容

        Returns:
            发送是否成功
        """
        if settings().FEISHU_WEBHOOK_URL.endswith(hookId):
            webhook_url = settings().FEISHU_WEBHOOK_URL
        elif settings().FEISHU_WEBHOOK_URL.endswith("/"):
            webhook_url = settings().FEISHU_WEBHOOK_URL + hookId
        else:
            webhook_url = settings().FEISHU_WEBHOOK_URL + "/" + hookId
        if not webhook_url:
            logger.error("飞书webhook URL未配置")
            return False

        # 处理标题和主题颜色
        processed_title = title
        theme_color = "green"  # 默认绿色

        if "[Alerting]" in title:
            processed_title = title.replace("[Alerting]", "告警: ")
            theme_color = "red"
        elif "[OK]" in title:
            processed_title = title.replace("[OK]", "恢复: ")
            theme_color = "green"

        try:
            # 构造飞书机器人消息格式
            message = {
                "msg_type": "interactive",
                "card": {
                    "schema": "2.0",
                    "body": {
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": content,
                            }
                        ],
                    },
                    "header": {
                        "title": {"content": processed_title, "tag": "plain_text"},
                        "template": theme_color,
                    },
                },
            }
            logger.info(f"飞书消息: {json.dumps(message, ensure_ascii=False)}")
            return True
            # async with httpx.AsyncClient() as client:
            #     response = await client.post(webhook_url, json=message, timeout=30)

            # if response.status_code == 200:
            #     result = response.json()
            #     if result.get("code") == 0:
            #         logger.info("飞书消息发送成功")
            #         return True
            #     else:
            #         logger.error(f"飞书消息发送失败: {result}")
            #         return False
            # else:
            #     logger.error(f"飞书webhook请求失败: {response.status_code}")
            #     return False

        except Exception as e:
            logger.error(f"发送飞书消息时发生错误: {str(e)}")
            return False

    def format_analysis_result_to_markdown(
        self, request: GrafanaRequest, analysis_data: GrafanaAlertAnalysisResponse
    ) -> str:
        """
        将分析结果格式化为markdown表格

        Args:
            request: GrafanaRequest grafana 告警回调请求体
            analysis_data: 分析结果对象

        Returns:
            格式化后的markdown字符串
        """
        markdown_content = (
            f"**告警链接**: [查看告警]({analysis_data.grafana_alert_link})\n"
        )
        if analysis_data.kibana_log_link:
            markdown_content += (
                f"**日志链接**: [查看日志]({analysis_data.kibana_log_link})\n"
            )

        if request.eval_matches and len(request.eval_matches) > 0:
            # 处理 eval_matches 信息
            eval_matches_content = "\n---\n"
            for i, match in enumerate(request.eval_matches, 1):
                metric = match.metric
                value = match.value

                # 尝试格式化数值
                try:
                    # 尝试转换为浮点数并格式化为两位小数
                    numeric_value = float(value)
                    formatted_value = f"{numeric_value:.2f}"
                except (ValueError, TypeError):
                    # 如果转换失败，使用原始值
                    formatted_value = str(value)

                eval_matches_content += f"   {metric} : {formatted_value} \n"
            # 需要添加一个空行, 否则会导致样式不正确
            markdown_content += eval_matches_content + "\n---\n"

        if analysis_data.analysis_result and analysis_data.analysis_result.errors:
            # 提取基本信息
            analysis_result = analysis_data.analysis_result
            errors = analysis_result.errors if analysis_result else []

            # 构建markdown表格
            row_number = 0
            for error_info in errors:
                error_msg = error_info.error or ""
                error_count = error_info.count or 0
                exception_detail = error_info.exception_details
                row_number += 1

                markdown_content += f"""
{row_number}. **错误信息**: {error_msg}
  - `出现次数`: {error_count}"""
                if exception_detail:
                    class_name = exception_detail.class_name or ""
                    method_name = exception_detail.method_name or ""
                    line_number = exception_detail.line_number or ""
                    exception_class = exception_detail.exception_class or ""

                    markdown_content += f"""
  - `类名`: {class_name}
  - `方法名`: {method_name}
  - `行号`: {line_number}
  - `异常类`: {exception_class}"""
                if exception_detail.github_source:
                    github_link = exception_detail.github_source.html_url or ""
                    markdown_content += f"""
  - `源码`: [github]({github_link})"""

                markdown_content += "\n---"

        return markdown_content


# 创建服务实例
feishu_webhook_service = FeishuWebhookService()
