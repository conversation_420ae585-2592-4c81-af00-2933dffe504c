import lark_oapi as lark
from lark_oapi.api.auth.v3 import (InternalAppAccessTokenRequest, InternalAppAccessTokenRequestBody,
                                   InternalAppAccessTokenResponse)
from lark_oapi.api.docx.v1 import (GetDocumentBlockRequest, GetDocumentBlockResponse, ListDocumentBlockRequest,
                                   ListDocumentBlockResponse)
from lark_oapi.api.drive.v1 import DownloadMediaRequest, DownloadMediaResponse
from lark_oapi.api.wiki.v2 import GetNodeSpaceRequest, GetNodeSpaceResponse

from app.core.config import settings


class FeishuService:
    def __init__(self):
        self.__client = None
        
    def client(self) :
        if not self.__client :
            self.__client = lark.Client.builder().app_id(settings().FEISHU_APP_ID).app_secret(settings().FEISHU_APP_SECRET).build()
        return self.__client

    def get_access_token(self) -> InternalAppAccessTokenResponse:
        """
        获取自建应用的app_access_token
        :return:
        """
        request: InternalAppAccessTokenRequest = InternalAppAccessTokenRequest.builder().request_body(
            InternalAppAccessTokenRequestBody.builder()
            .app_id(settings().FEISHU_APP_ID)
            .app_secret(settings().FEISHU_APP_SECRET).build()
        ).build()
        response: InternalAppAccessTokenResponse = self.client().auth.v3.app_access_token.internal(request)
        return response

    def get_node_info(self, doc_token: str) -> GetNodeSpaceResponse:
        """
        获取知识空间节点信息
        :param doc_token:
        :return:
        """
        request: GetNodeSpaceRequest = (GetNodeSpaceRequest.builder()
                                        .token(doc_token)
                                        .build())
        response: GetNodeSpaceResponse = self.client().wiki.v2.space.get_node(request)
        return response

    def list_blocks(self, doc_id: str) -> ListDocumentBlockResponse:
        """
        获取文档的所有块
        :param doc_id:
        :return:
        """
        request: ListDocumentBlockRequest = (ListDocumentBlockRequest.builder()
                                             .document_id(doc_id)
                                             .build())
        response: ListDocumentBlockResponse = self.client().docx.v1.document_block.list(request)
        return response

    def get_block(self, doc_id: str, block_id: str) -> GetDocumentBlockResponse:
        """
        获取文档块的内容
        :param doc_id:
        :param block_id:
        :return:
        """
        request: GetDocumentBlockRequest = (GetDocumentBlockRequest.builder()
                                            .document_id(doc_id)
                                            .block_id(block_id)
                                            .build())
        response: GetDocumentBlockResponse = self.client().docx.v1.document_block.get(request)
        return response

    def download_media(self, file_token: str) -> DownloadMediaResponse:
        """
        下载素材
        :param file_token:
        :return:
        """
        request: DownloadMediaRequest = (DownloadMediaRequest.builder()
                                         .file_token(file_token)
                                         .build())
        response: DownloadMediaResponse = self.client().drive.v1.media.download(request)
        return response


feishu_service = FeishuService()
