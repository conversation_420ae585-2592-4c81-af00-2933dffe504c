from app.core.config import settings
from app.external_services import deps
from app.external_services.uac.schemas import (
    GetUserInfoExtRequest,
    GetUserInfoExtResponse,
    GetUserInfoRequest,
    UserInfoResponse,
)


class UacService:

    def get_userinfo(self, req: GetUserInfoRequest) -> UserInfoResponse:
        url = f"{settings().RPC_UAC_URL}/export/uac/user/getUserInfo"
        result = deps.request(url, req, UserInfoResponse)
        return result

    def get_userinfoext_by_sellerid(
        self, req: GetUserInfoExtRequest
    ) -> GetUserInfoExtResponse:
        url = f"{settings().RPC_UAC_URL}/export/uac/ext/getUserInfoExtBySellerId"
        result = deps.request(url, req, GetUserInfoExtResponse)
        return result


uac_service = UacService()
