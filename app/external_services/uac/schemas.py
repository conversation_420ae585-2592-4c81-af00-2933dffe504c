from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, ConfigDict, model_validator
from typing_extensions import Self


class GetUserInfoRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    sellerNick: Optional[str] = None
    sellerId: Optional[str] = None
    platformId: str
    appName: Optional[str] = None
    memberId: Optional[str] = None
    shopId: Optional[str] = None

    @model_validator(mode='after')
    def validator(self) -> Self:
        if not self.sellerId and not self.sellerNick:
            raise ValueError('sellerNick 和 sellerId 不能同时为空')
        return self


class UserInfoResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    platformId: Optional[str] = None
    appName: Optional[str] = None
    sellerId: Optional[str] = None
    corpId: Optional[str] = None
    sellerNick: Optional[str] = None
    topSession: Optional[str] = None
    sellerAppId: Optional[str] = None
    sellerAppSecret: Optional[str] = None
    memberId: Optional[str] = None
    roleId: Optional[str] = None
    vipflag: Optional[int] = None
    hVersion: Optional[str] = None
    hasNeedAuth: Optional[bool] = None
    tag: Optional[str] = None
    createDate: Optional[datetime] = None
    revivalDate: Optional[datetime] = None
    orderCycleEnd: Optional[datetime] = None
    authDeadLine: Optional[datetime] = None
    articleCode: Optional[str] = None
    itemCode: Optional[List[str]] = None
    hasExist: Optional[bool] = None
    fuwuMarketOrderCycleEnd: Optional[datetime] = None
    fuwuMarketVipflag: Optional[int] = None
    mallName: Optional[str] = None
    ayMultiTags: Optional[List[str]] = None
    shopId: Optional[str] = None
    shopCipher: Optional[str] = None


class UserExtInfoDTO(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    sellerId: str
    sellerNick: str
    storeId: str
    corpId: Optional[str] = None
    topStatus: Optional[str] = None
    topTrade_count: Optional[int] = None
    dbId: Optional[int] = None
    dbStatus: Optional[int] = None
    pullStatus: Optional[int] = None
    apiStatus: Optional[int] = None
    pullStartDateTime: Optional[datetime] = None
    pullEndDateTime: Optional[datetime] = None
    pullEndPoint: Optional[bool] = None
    appName: Optional[str] = None
    memberId: Optional[str] = None
    products: Optional[List[str]] = None


class GetUserInfoExtRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    sellerId: str
    storeId: Optional[str] = None
    appName: Optional[str] = None
    businessId: Optional[str] = None
    memberId: Optional[str] = None


class GetUserInfoExtResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    userInfoExt: Optional[UserExtInfoDTO] = None
