import os
from typing import Optional

import oss2
from oss2.api import Bucket
from oss2.credentials import StaticCredentialsProvider

from app.core.config import settings


class OssService:

    def __init__(self):
        self.__bucket : Optional[Bucket] = None

    def bucket(self):
        if not self.__bucket:
            auth = oss2.ProviderAuth(StaticCredentialsProvider(
                settings().OSS_ACCESS_KEY_ID,
                settings().OSS_ACCESS_KEY_SECRET
            ))
            endpoint = settings().OSS_ENDPOINT
            region = settings().OSS_REGION
            self.__bucket = oss2.Bucket(auth, endpoint, 'qniyong', region=region)
        return self.__bucket


    def put_object_from_file(self, object_name: str, file_path: str):
        """
        把本地文件上传到OSS
        :param object_name:
        :param file_path:
        :return:
        """
        self.bucket().put_object_from_file(object_name, file_path)


oss_service = OssService()
