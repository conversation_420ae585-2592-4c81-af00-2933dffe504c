from contextlib import contextmanager
from typing import Any, Iterator, Annotated, Optional

from fastapi import Depends
from sqlalchemy import create_engine, Connection
from sqlalchemy.orm import Session, sessionmaker

from app.core.config import settings


class DatabaseSessionManager:
    def __init__(self, host: str, engine_kwargs: dict[str, Any] = {}):
        self._engine = create_engine(host, **engine_kwargs)
        self._sessionmaker = sessionmaker(autocommit=False, bind=self._engine)

    def close(self):
        if self._engine is None:
            raise Exception("DatabaseSessionManager is not initialized")
        self._engine.dispose()

        self._engine = None
        self._sessionmaker = None

    @contextmanager
    def connect(self) -> Iterator[Connection]:
        if self._engine is None:
            raise Exception("DatabaseSessionManager is not initialized")

        with self._engine.begin() as connection:
            try:
                yield connection
            except Exception:
                connection.rollback()
                raise

    @contextmanager
    def session(self) -> Iterator[Session]:
        if self._sessionmaker is None:
            raise Exception("DatabaseSessionManager is not initialized")

        session_local = self._sessionmaker()
        try:
            yield session_local
        except Exception:
            session_local.rollback()
            raise
        finally:
            session_local.close()


__sessionmanager = Optional[DatabaseSessionManager] = None


def get_db_session():
    global __sessionmanager
    if __sessionmanager is None:
        __sessionmanager = DatabaseSessionManager(
            settings().DATABASE_URL,
            {
                "pool_size": settings().DATABASE_POOL_SIZE,
                "max_overflow": settings().DATABASE_POOL_OVERFLOW,
                "pool_timeout": 10,
                "pool_recycle": 3600,
            },
        )
    with __sessionmanager.session() as session:
        yield session


DBSessionDepends = Annotated[Session, Depends(get_db_session)]
