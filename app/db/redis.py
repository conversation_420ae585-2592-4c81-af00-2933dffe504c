import logging
from typing import Optional

from redis.asyncio.client import Redis

from app.core.config import settings

__redis: Optional[Redis] = None


def __get_redis() -> Redis:
    """
    获取redis连接池
    :return:
    """
    global __redis
    if __redis:
        return __redis
    logging.info(f"连接 redis {settings().REDDIS_HOST}:{settings().REDDIS_PORT}:{settings().REDDIS_DB}")
    __redis = Redis.from_url(
        f"redis://{settings().REDDIS_HOST}:{settings().REDDIS_PORT}",
        password=settings().REDDIS_PASSWORD,
        db=settings().REDDIS_DB,
        max_connections=settings().REDDIS_POOL_SIZE,
        encoding="utf-8",
        decode_responses=True,
    )
    return __redis


redis_client = __get_redis
