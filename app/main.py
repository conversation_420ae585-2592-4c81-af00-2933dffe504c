from dotenv import load_dotenv

from app.log import setup_logging

# 加载 .env 文件
load_dotenv()

log_config = setup_logging()

import logging
import os
from contextlib import AsyncExitStack, asynccontextmanager

from fastapi import FastAPI
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from starlette.exceptions import HTTPException
from starlette.responses import JSONResponse

from app.api.endpoints import crawl, dify_plugin, grafana, liveness, tools
from app.core.config import lifespan as settings_lifespan
from app.services.crawl.item_crawl_service import lifespan as crawler_lifespan

logging = logging.getLogger(__name__)


def app_lifespan(lifespans: list):
    @asynccontextmanager
    async def _lifespan_manager(app: FastAPI):
        exit_stack = AsyncExitStack()
        async with exit_stack:
            for lifespan in lifespans:
                await exit_stack.enter_async_context(lifespan(app))
            yield

    return _lifespan_manager


server_url = os.environ.get("SERVER_URL", f"http://localhost:8000")
app = FastAPI(
    lifespan=app_lifespan([settings_lifespan, crawler_lifespan]),
    title="aigc-rag-service",
    servers=[{"url": server_url}],
)


@app.exception_handler(HTTPException)
async def validation_exception_handler(request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"code": exc.status_code, "message": exc.detail},
    )


@app.exception_handler(ValidationError)
async def validation_exception_handler(request, exc):
    logging.exception(exc)
    return JSONResponse(status_code=400, content={"code": 400, "message": str(exc)})


@app.exception_handler(RequestValidationError)
async def request_validation_exception_handler(request, exc):
    logging.exception(exc)
    return JSONResponse(status_code=400, content={"code": 400, "message": exc.errors()})


@app.exception_handler(Exception)
async def http_exception_handler(request, exc):
    logging.exception(exc)
    return JSONResponse(
        status_code=500, content={"code": 500, "message": "Inner Server Error"}
    )


app.include_router(liveness.router)
app.include_router(crawl.router)
app.include_router(dify_plugin.router)
app.include_router(grafana.router)
app.include_router(tools.router)

if __name__ == "__main__":
    import uvicorn

    host = os.environ.get("SERVER_HOST", "0.0.0.0")
    port = os.environ.get("SERVER_PORT", "8000")
    workers = os.environ.get("SERVER_WORKERS", "1")
    reload = (
        True if os.environ.get("SERVER_RELOAD", "False").lower() == "true" else False
    )
    logging.info(f"host: {host}, port: {port}, workers: {workers}, reload: {reload}")

    uvicorn.run(
        "app.main:app",
        host=host,
        port=int(port),
        workers=int(workers),
        log_config=log_config,
        reload=reload,
    )
