from app.core.config import settings
from app.db.redis import redis_client


class CrawlRedisDao:
    def __init__(self):
        pass

    async def get_item_shipping(self, item_id: str, sku_id: str) -> str:
        """
        获取商品货运信息
        :param item_id:
        :param sku_id:
        :return:
        """
        key = f"rag:crwal:shipping:{item_id}:{sku_id}"
        return await redis_client().get(key)

    async def set_item_shipping(self, item_id: str, sku_id: str, shipping: str):
        """
        设置商品货运信息
        :param item_id:
        :param sku_id:
        :param shipping:
        :return:
        """
        key = f"rag:crwal:shipping:{item_id}:{sku_id}"
        await redis_client().set(
            key, shipping, ex=settings().CRAWL_TEIM_SHIPPING_TIMEOUT
        )  # 1天过期

    async def set_storage_state(self, username: str, storage_state: str):
        """
        设置存储状态
        :param username:
        :param storage_state:
        :return:
        """
        key = f"rag:crwal:storage_state"
        await redis_client().set(key, storage_state)

    async def get_storage_state(self, username: str):
        """
        获取存储状态
        :param username:
        :return:
        """
        key = f"rag:crwal:storage_state"
        return await redis_client().get(key)


crawl_redis_dao = CrawlRedisDao()
