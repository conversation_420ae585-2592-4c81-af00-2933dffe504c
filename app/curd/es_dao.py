import json
import logging
from typing import Dict, List, Optional

import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)


class EsDao:
    """
    Elasticsearch 数据访问
    """

    def __init__(self):
        pass

    async def query_by_timestamp_range(
        self,
        query: str,
        index: str,
        time_range_minutes: int,
    ) -> List[Dict]:
        """查询Elasticsearch获取数据"""
        try:
            # 构建ES查询体
            query_body = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "range": {
                                    "@timestamp": {
                                        "gte": f"now-{time_range_minutes}m",
                                        "lte": "now",
                                    }
                                }
                            }
                        ]
                    }
                },
                "sort": [{"@timestamp": {"order": "desc"}}],
                "size": 10,
                "_source": ["message", "stack_trace", "@timestamp", "level"],
            }

            # 如果es_query包含具体的查询条件，尝试解析并添加
            if query and query.strip():
                try:
                    # 尝试解析为JSON查询
                    parsed_query = json.loads(query)
                    if "query" in parsed_query:
                        query_body["query"]["bool"]["must"].append(
                            parsed_query["query"]
                        )
                except json.JSONDecodeError:
                    # 如果不是JSON，作为查询字符串处理
                    query_body["query"]["bool"]["must"].append(
                        {"query_string": {"query": query}}
                    )

            # 构建查询URL，包含索引
            es_url = settings().LOG_ES_URL
            if index:
                # 处理索引模式（可能包含通配符）
                url = f"{es_url}/{index}/_search"
            else:
                # 如果没有指定索引，使用默认搜索
                url = f"{es_url}/_search"

            headers = {"Content-Type": "application/json"}

            logger.info(f"查询ES索引: {index}, URL: {url}")

            es_auth = (settings().LOG_ES_USERNAME, settings().LOG_ES_PASSWORD)

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url, json=query_body, headers=headers, auth=es_auth, timeout=60
                )

            if response.status_code == 200:
                result = response.json()
                hits = result.get("hits", {}).get("hits", [])
                logger.info(f"ES查询成功，返回 {len(hits)} 条记录")
                return [
                    {
                        **hit.get("_source", {}),
                        "_id": hit.get("_id"),
                        "_index": hit.get("_index"),
                    }
                    for hit in hits
                ]
            else:
                logger.error(f"ES查询失败: {response.status_code}, {response.text}")
                return []

        except Exception as e:
            logger.error(f"查询Elasticsearch时发生错误: {str(e)}")
            return []

    async def query_by_id(self, index: str, doc_id: str) -> Optional[Dict]:
        """根据ID查询单条日志记录"""
        try:
            es_url = settings().LOG_ES_URL
            url = f"{es_url}/{index}/_doc/{doc_id}"

            headers = {"Content-Type": "application/json"}
            es_auth = (settings().LOG_ES_USERNAME, settings().LOG_ES_PASSWORD)

            logger.info(f"查询ES文档: index={index}, id={doc_id}")

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    url, headers=headers, auth=es_auth, timeout=60
                )

            if response.status_code == 200:
                result = response.json()
                source = result.get("_source", {})
                return source
            else:
                logger.error(f"ES查询失败: {response.status_code}, {response.text}")
                return None

        except Exception as e:
            logger.error(f"查询Elasticsearch时发生错误: {str(e)}")
            return None

    async def query_by_timestamp_and_query_string(
        self, index: str, query_string: str, timestamp: str
    ) -> List[Dict]:
        """根据时间戳和查询字符串查询日志记录"""
        try:
            es_url = settings().LOG_ES_URL
            url = f"{es_url}/{index}/_search"

            # 构建查询体
            query_body = {
                "query": {
                    "bool": {
                        "must": [
                            {"query_string": {"query": query_string}},
                            {
                                "range": {
                                    "@timestamp": {
                                        "gte": f"{timestamp}||-10m",
                                        "lte": f"{timestamp}||+10m",
                                    }
                                }
                            },
                        ]
                    }
                },
                "sort": [{"@timestamp": {"order": "desc"}}],
                "size": 100,  # 返回最多100条记录
            }

            headers = {"Content-Type": "application/json"}
            es_auth = (settings().LOG_ES_USERNAME, settings().LOG_ES_PASSWORD)

            logger.info(
                f"查询ES: index={index}, query_string={query_string}, timestamp={timestamp}"
            )

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url, json=query_body, headers=headers, auth=es_auth, timeout=60
                )

            if response.status_code == 200:
                result = response.json()
                hits = result.get("hits", {}).get("hits", [])
                logger.info(f"ES查询成功，返回 {len(hits)} 条记录")
                return [hit.get("_source", {}) for hit in hits]
            else:
                logger.error(f"ES查询失败: {response.status_code}, {response.text}")
                return []

        except Exception as e:
            logger.error(f"查询Elasticsearch时发生错误: {str(e)}")
            return []


es_dao = EsDao()
