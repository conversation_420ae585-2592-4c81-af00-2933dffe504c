from sqlalchemy import select
from sqlalchemy.orm import Session

from app.models.user import UserProductinfoTradeExt

class UserDao:

    def get_user_ext(self, db: Session) -> UserProductinfoTradeExt:
        statm = select(UserProductinfoTradeExt).where(UserProductinfoTradeExt.top_status == '10').limit(1)
        user: UserProductinfoTradeExt = db.scalars(statm).first()
        return user

user_dao = UserDao()
