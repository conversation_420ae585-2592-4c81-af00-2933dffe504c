import logging
import os
from typing import IO


def write_io_to_path(io_stream: IO[bytes], save_path: str):
    """
    把io流写入到指定文件中
    :param io_stream:
    :param save_path:
    :return:
    """
    # 获取目录路径
    directory = os.path.dirname(save_path)

    # 如果目录不存在，则创建目录
    if not os.path.exists(directory):
        os.makedirs(directory)

    # 将 IO 流的内容写入到指定路径
    with open(save_path, "wb") as file:
        # 移动光标到开始位置
        io_stream.seek(0)
        # 读取内容并写入文件
        file.write(io_stream.read())


def delete_file(save_path: str):
    """
    删除文件
    :param save_path:
    :return:
    """
    try:
        # 检查文件是否存在
        if os.path.isfile(save_path):
            os.remove(save_path)  # 删除文件
            logging.info(f"文件已成功删除: {save_path}")
        else:
            logging.warning(f"文件不存在: {save_path}")
    except PermissionError:
        logging.error(f"权限错误: 无法删除文件 {save_path}")
    except Exception as e:
        logging.error(f"发生错误: {e}")
