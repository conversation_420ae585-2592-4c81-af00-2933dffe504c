from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import base64

__iv = "0000000000000000"


# 加密函数
def encrypt(key: str, text: str):
    global __iv
    # 加密内容需要至少16位密码，不足用空格补齐
    cryptor = AES.new(
        base64.b64decode(key.encode("utf-8")), AES.MODE_CBC, __iv.encode("utf-8")
    )
    text = text.encode("utf-8")
    text = pad(text, AES.block_size)
    ciphertext = cryptor.encrypt(text)
    # 将加密后的数据进行base64编码
    return base64.b64encode(ciphertext).decode("utf-8")


# 解密函数
def decrypt(key: str, text: str):
    global __iv
    cryptor = AES.new(
        base64.b64decode(key.encode("utf-8")), AES.MODE_CBC, __iv.encode("utf-8")
    )
    # 先对base64解密成bytes格式，并解密后返回bytes数据
    decrypts = cryptor.decrypt(base64.b64decode(text.encode("utf-8")))
    decrypts = unpad(decrypts, AES.block_size)
    return str(decrypts.decode("utf-8"))
    