import random, time
from playwright.sync_api import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    Request,
    sync_playwright,
)
from python_ghost_cursor.playwright_sync import create_cursor


def __drag_verification_slider(selector: str, page: Page, retry: int = 1) -> bool:
    """
    拖动验证滑块
    :param page: 页面对象
    :param retry: 重试次数
    :return: 是否成功
    """
    if retry > 2:
        print("重试次数过多")
        return False
    print(f"第 {retry} 次登录尝试")
    content_frame = page.locator(selector).content_frame
    element = content_frame.get_by_role("button", name="滑块")
    if not element:
        print("获取不到滑块")
        return False
    box = element.bounding_box()
    if not box:
        print("获取位置出错")
        return False

    element.hover()
    s_x = box["x"] + box["width"] / 2 + random.randint(1, 3)
    s_y = box["y"] + box["height"] / 2 + random.randint(-3, 3)
    page.mouse.move(s_x, s_y)
    page.mouse.down()

    e_x = box["x"] - box["width"] / 2 + 350
    e_y = box["y"] + box["height"] / 2
    cursor = create_cursor(page)
    cursor.overshoot_radius
    cursor.move(element)
    cursor.move_to({"x": e_x, "y": e_y})
    page.mouse.up()
    __rest(0.5)
    f = page.locator(selector)
    if not f or f.count() == 0:
        return True
    element = f.content_frame.locator("#\\`nc_1_refresh1\\`")
    if element and element.count() > 0 and element.is_visible():
        element.hover()
        element.click()
        return __drag_verification_slider(selector=selector, page=page, retry=retry + 1)


def __rest(self, timeout: float = 0.4):
    """
    延迟一小会儿
    :param timeout: 时间
    """
    time.sleep(random.random() / 2 + timeout)


with sync_playwright() as p:
    browser = p.chromium.launch(headless=False, devtools=False, chromium_sandbox=False)
    # browser = p.chromium.connect("ws://127.0.0.1:3000/")
    # browser = p.chromium.connect("ws://172.18.5.187:30088/")
    page: Page = None
    try:
        page = browser.new_page(
            storage_state="./.storage/storage_state.json",
            user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            extra_http_headers={
                # "Accept-Language": "",
                'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134"',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            },
            # proxy={"server":"http://**********:1086"}
            # proxy={"server":"http://ddsq.aiyongtech.com:1080"}
            # proxy={"server":"http://127.0.0.1:3128"}
        )
        page.set_default_timeout(30000)
        page.add_init_script("delete Object.getPrototypeOf(navigator).webdriver")
        page.goto(
            "https://detail.tmall.com/item.htm?id=************&skuId=5923513487055"
        )
        # page.pause()
        # # Set up a request interceptor to log headers
        page.on("request", lambda request: print(f"Request URL: {request.url}\nHeaders: {request.headers}"))

        # # Optional: You can also log response headers if needed
        # page.on("response", lambda response: print(f"Response URL: {response.url}\nStatus: {response.status}\nHeaders: {response.headers}"))

        # print("Starting navigation with header logging enabled...")
        page.wait_for_selector("#purchasePanel,.J_MIDDLEWARE_FRAME_WIDGET")
        # page.pause()
        e = page.locator(".J_MIDDLEWARE_FRAME_WIDGET > iframe")
        if e and e.count() > 0:
            print("需要网络验证")
            page.screenshot(path=f"./.screenshots/screenshot_net.png", full_page=True)
            __drag_verification_slider(".J_MIDDLEWARE_FRAME_WIDGET > iframe", page, 1)
            page.wait_for_timeout(5000)
        else:
            print("不需要网络验证")
        page.pause()
        
        e = page.locator('[class*="shipping--"]')
        if e and e.count() > 0:
            print(f"shipping={e.inner_text()}")
    finally:
        if page:
            page.context.storage_state(path="./.storage/storage_state.json")
            page.screenshot(path=f"./.screenshots/screenshot_end.png", full_page=True)
            # with open("./page.html", "w", encoding="utf-8") as file:
            #     file.write(page.content())
