import random, time
from playwright.sync_api import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    sync_playwright,
)
from python_ghost_cursor.playwright_async import create_cursor


def __drag_verification_slider(self, page: Page, retry: int = 1) -> bool:
    """
    拖动验证滑块
    :param page: 页面对象
    :param retry: 重试次数
    :return: 是否成功
    """
    if retry > 5:
        print("重试次数过多")
        return False
    print(f"第 {retry} 次登录尝试")
    content_frame = page.locator(".J_MIDDLEWARE_FRAME_WIDGET > iframe").content_frame
    element = content_frame.get_by_role("button", name="滑块")
    if not element:
        print("获取不到滑块")
        return False
    box = element.bounding_box()
    if not box:
        print("获取位置出错")
        return False

    element.hover()
    s_x = box["x"] + box["width"] / 2 + random.randint(1, 3)
    s_y = box["y"] + box["height"] / 2 + random.randint(-3, 3)
    page.mouse.move(s_x, s_y)
    page.mouse.down()

    e_x = box["x"] - box["width"] / 2 + 400
    e_y = box["y"] + box["height"] / 2
    cursor = create_cursor(page)
    cursor.move(element)
    cursor.move_to({"x": e_x, "y": e_y})
    page.mouse.up()
    page.wait_for_function(
        """
        () => {
            var f = document.getElementById("baxia-dialog-content");
            if (f) {
                var fd = f.contentDocument || f.contentWindow.document;
                var r = fd.querySelector("#\\\\`nc_1_refresh1\\\\`");
                if (r && r.style.display != 'none') {
                    return true;
                }
            }
            var e = document.querySelector(".dialog-btn.dialog-btn-feedback.primary");
            return e && e.style.display != 'none';
        }
        """
    )
    element = content_frame.locator("#\\`nc_1_refresh1\\`")
    if element and element.is_visible():
        __rest(0.5)
        element.hover()
        element.click()
        return __drag_verification_slider(page, retry + 1)
    else:
        return True


def __rest(self, timeout: float = 0.4):
    """
    延迟一小会儿
    :param timeout: 时间
    """
    time.sleep(random.random() / 2 + timeout)


with sync_playwright() as p:
    browser = p.chromium.launch(headless=False, devtools=False, chromium_sandbox=False)
    page = browser.new_page(
        user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        extra_http_headers={
            "Accept-Language": "",
        },
    )
    page.add_init_script("delete Object.getPrototypeOf(navigator).webdriver")
    page.goto("https://detail.tmall.com/item.htm?id=************&skuId=5923513487055")
    page.pause()
