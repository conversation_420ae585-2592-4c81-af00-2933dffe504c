import random, time
from playwright.sync_api import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    sync_playwright,
)
from python_ghost_cursor.playwright_sync import create_cursor


def __drag_verification_slider(selector: str, page: Page, retry: int = 1) -> bool:
    """
    拖动验证滑块
    :param page: 页面对象
    :param retry: 重试次数
    :return: 是否成功
    """
    if retry > 5:
        print("重试次数过多")
        return False
    print(f"第 {retry} 次登录尝试")
    content_frame = page.locator(selector).content_frame
    element = content_frame.get_by_role("button", name="滑块")
    if not element:
        print("获取不到滑块")
        return False
    box = element.bounding_box()
    if not box:
        print("获取位置出错")
        return False

    element.hover()
    s_x = box["x"] + box["width"] / 2 + random.randint(1, 3)
    s_y = box["y"] + box["height"] / 2 + random.randint(-3, 3)
    page.mouse.move(s_x, s_y)
    page.mouse.down()

    e_x = box["x"] - box["width"] / 2 + 350
    e_y = box["y"] + box["height"] / 2
    cursor = create_cursor(page)
    cursor.move(element)
    cursor.move_to({"x": e_x, "y": e_y})
    page.mouse.up()
    # page.wait_for_function(
    #     f"""
    #     () => {{
    #         var f = document.querySelector("{selector}");
    #         if (f) {{
    #             var fd = f.contentDocument || f.contentWindow.document;
    #             var r = fd.querySelector("#\\\\`nc_1_refresh1\\\\`");
    #             if (r && r.style.display != 'none') {{
    #                 return true;
    #             }}
    #         }}
    #         var e = document.querySelector(".dialog-btn.dialog-btn-feedback.primary");
    #         return e && e.style.display != 'none';
    #     }}
    #     """
    # )
    __rest(3)
    element = content_frame.locator("#\\`nc_1_refresh1\\`")
    page.screenshot(path=f"./screenshot/screenshot_{retry}.png", full_page=True)
    if element and element.is_visible():
        __rest(0.5)
        element.hover()
        element.click()
        return __drag_verification_slider(selector=selector, page=page, retry=retry + 1)
    __rest(3)


def __rest(self, timeout: float = 0.4):
    """
    延迟一小会儿
    :param timeout: 时间
    """
    time.sleep(random.random() / 2 + timeout)


with sync_playwright() as p:
    browser = p.chromium.launch(
        headless=False,
        devtools=False,
        chromium_sandbox=False,
        args=[
            "--disable-infobars",
            "--disable-web-security",
            "--disable-gpu",
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-extensions",
            "--disable-dev-shm-usage",
        ],
    )
    page: Page = None
    try:
        page = browser.new_page(
            user_agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            extra_http_headers={
                "Accept-Language": "en;q=0.9",
            },
        )
        page.add_init_script("delete Object.getPrototypeOf(navigator).webdriver")
        page.goto("file:///Users/<USER>/workspace/git/aigc-rag-service/page.html")
        e = page.wait_for_selector(".J_MIDDLEWARE_FRAME_WIDGET > iframe", timeout=10000)
        if e and e.content_frame():
            print("需要网络验证")
            __drag_verification_slider(".J_MIDDLEWARE_FRAME_WIDGET > iframe", page, 1)
            page.wait_for_timeout(5000)
        else:
            print("不需要网络验证")
            print(e.inner_text())
    finally:
        if page:
            page.screenshot(path=f"./screenshot/screenshot_end.png", full_page=True)
            with open("./page.html", "w", encoding="utf-8") as file:
                file.write(page.content())
