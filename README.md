# aiyong_aigc_rag_service

## Overview
AIGC RAG 相关服务

This project is a FastAPI-based service.

## Requirements

- Python 3.12+
- FastAPI
- SQLAlchemy
- Redis
- Pydantic
- poetry

## Installation

1. Clone the repository:
    ```sh
    git clone <repository_url>
    cd <repository_directory>
    ```

2. install poetry
    ```sh
    pipx install poetry
    ```

3. Create and activate a virtual environment:
    ```sh
    conda create -n aiyong_aigc_rag_service python=3.12
    conda activate aiyong_aigc_rag_service 
    ```

4. Install the dependencies:
    ```sh
    poetry install
    ```

5. Set up environment variables:
   `.dev.env` file is dev environment file.
   `.env` file is production environment file.

## Configuration

The application configuration is managed using Pydantic settings. The configuration can be found in
`app/config/global_config.py`.

## Running the Application

To run the application in development mode:

```sh
export PYTHONPATH=$PWD
python ./app/main.py
```
